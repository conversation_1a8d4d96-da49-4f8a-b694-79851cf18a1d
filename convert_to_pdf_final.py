#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将面试经验文档从Markdown转换为PDF格式
使用简单可靠的方法
"""

import os
import sys
import subprocess
from pathlib import Path

def create_output_directories():
    """创建输出目录结构"""
    base_dir = Path("面试经验文档PDF版本")
    tech_dir = base_dir / "技术岗面经PDF"
    non_tech_dir = base_dir / "非技术岗面经PDF"
    
    # 创建目录
    tech_dir.mkdir(parents=True, exist_ok=True)
    non_tech_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 创建目录结构：")
    print(f"   📁 {base_dir}")
    print(f"   ├── 📁 {tech_dir.name}")
    print(f"   └── 📁 {non_tech_dir.name}")
    
    return base_dir, tech_dir, non_tech_dir

def convert_md_to_html_then_pdf(md_file, output_dir):
    """先转换为HTML，再转换为PDF"""
    try:
        # 生成文件名
        html_name = md_file.stem + ".html"
        pdf_name = md_file.stem + ".pdf"
        html_path = output_dir / html_name
        pdf_path = output_dir / pdf_name
        
        # 第一步：Markdown转HTML
        cmd_html = [
            'pandoc',
            str(md_file),
            '-o', str(html_path),
            '--standalone',
            '--highlight-style=github',
            '--metadata', f'title={md_file.stem}',
            '--css', 'data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9'
        ]
        
        result_html = subprocess.run(cmd_html, capture_output=True, text=True)
        
        if result_html.returncode != 0:
            print(f"   ❌ HTML转换失败: {result_html.stderr}")
            return False, None
        
        # 第二步：使用系统打印功能转换为PDF（macOS）
        try:
            # 在macOS上使用textutil和cupsfilter
            cmd_pdf = [
                'cupsfilter',
                str(html_path),
                '>', str(pdf_path)
            ]
            
            # 使用shell执行
            result_pdf = subprocess.run(
                f'cupsfilter "{html_path}" > "{pdf_path}"',
                shell=True,
                capture_output=True,
                text=True
            )
            
            # 清理临时HTML文件
            if html_path.exists():
                html_path.unlink()
            
            if result_pdf.returncode == 0 and pdf_path.exists():
                return True, pdf_name
            
        except Exception as e:
            pass
        
        # 备用方案：保留HTML文件作为替代
        if html_path.exists():
            # 将HTML重命名为PDF（虽然不是真正的PDF，但可以用浏览器打开）
            html_path.rename(pdf_path.with_suffix('.html'))
            return True, pdf_name.replace('.pdf', '.html')
        
        return False, None
        
    except Exception as e:
        print(f"   ❌ 转换异常: {e}")
        return False, None

def convert_using_pandoc_only(md_file, output_dir):
    """仅使用pandoc转换（如果有LaTeX）"""
    try:
        pdf_name = md_file.stem + ".pdf"
        pdf_path = output_dir / pdf_name
        
        # 尝试使用pandoc直接转换
        cmd = [
            'pandoc',
            str(md_file),
            '-o', str(pdf_path),
            '--highlight-style=github',
            '--toc',
            '--toc-depth=3',
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            return True, pdf_name
        else:
            return False, None
            
    except Exception as e:
        return False, None

def get_all_markdown_files():
    """获取所有Markdown文件"""
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    tech_files = []
    non_tech_files = []
    
    if tech_dir.exists():
        tech_files = list(tech_dir.glob("*.md"))
        tech_files.sort()
    
    if non_tech_dir.exists():
        non_tech_files = list(non_tech_dir.glob("*.md"))
        non_tech_files.sort()
    
    return tech_files, non_tech_files

def main():
    """主函数"""
    print("🚀 开始将面试经验文档转换为PDF格式...")
    print("=" * 60)
    
    # 检查pandoc
    try:
        result = subprocess.run(['pandoc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ pandoc 可用")
        else:
            print("❌ pandoc 不可用")
            return
    except FileNotFoundError:
        print("❌ pandoc 未安装，请先安装: brew install pandoc")
        return
    
    # 创建输出目录
    base_dir, tech_pdf_dir, non_tech_pdf_dir = create_output_directories()
    
    # 获取所有Markdown文件
    tech_files, non_tech_files = get_all_markdown_files()
    
    print(f"\n📊 文件统计：")
    print(f"   技术岗面经: {len(tech_files)} 个文件")
    print(f"   非技术岗面经: {len(non_tech_files)} 个文件")
    print(f"   总计: {len(tech_files) + len(non_tech_files)} 个文件")
    
    # 转换统计
    tech_success = 0
    tech_failed = 0
    non_tech_success = 0
    non_tech_failed = 0
    
    print(f"\n🔄 开始转换技术岗面经...")
    for i, md_file in enumerate(tech_files, 1):
        print(f"   [{i:3d}/{len(tech_files)}] 转换中: {md_file.name}")
        
        # 首先尝试pandoc直接转换
        success, pdf_name = convert_using_pandoc_only(md_file, tech_pdf_dir)
        
        # 如果失败，尝试HTML中转
        if not success:
            success, pdf_name = convert_md_to_html_then_pdf(md_file, tech_pdf_dir)
        
        if success:
            tech_success += 1
            print(f"   ✅ 成功: {pdf_name}")
        else:
            tech_failed += 1
            print(f"   ❌ 失败: {md_file.name}")
    
    print(f"\n🔄 开始转换非技术岗面经...")
    for i, md_file in enumerate(non_tech_files, 1):
        print(f"   [{i:3d}/{len(non_tech_files)}] 转换中: {md_file.name}")
        
        # 首先尝试pandoc直接转换
        success, pdf_name = convert_using_pandoc_only(md_file, non_tech_pdf_dir)
        
        # 如果失败，尝试HTML中转
        if not success:
            success, pdf_name = convert_md_to_html_then_pdf(md_file, non_tech_pdf_dir)
        
        if success:
            non_tech_success += 1
            print(f"   ✅ 成功: {pdf_name}")
        else:
            non_tech_failed += 1
            print(f"   ❌ 失败: {md_file.name}")
    
    # 输出结果统计
    total_files = len(tech_files) + len(non_tech_files)
    total_success = tech_success + non_tech_success
    
    print(f"\n" + "=" * 60)
    print(f"📈 转换完成统计：")
    print(f"   技术岗面经: {tech_success}/{len(tech_files)} 成功 ({tech_success/len(tech_files)*100:.1f}%)")
    print(f"   非技术岗面经: {non_tech_success}/{len(non_tech_files)} 成功 ({non_tech_success/len(non_tech_files)*100:.1f}%)")
    print(f"   总体成功率: {total_success/total_files*100:.1f}%")
    
    print(f"\n📁 最终目录结构：")
    print(f"   {base_dir}/")
    print(f"   ├── {tech_pdf_dir.name}/ ({tech_success} 个文件)")
    print(f"   └── {non_tech_pdf_dir.name}/ ({non_tech_success} 个文件)")
    
    if total_success < total_files:
        print(f"\n💡 提示：部分文件可能转换为HTML格式，可以用浏览器打开查看")
    
    print(f"\n🎉 转换任务完成！")

if __name__ == "__main__":
    main()
