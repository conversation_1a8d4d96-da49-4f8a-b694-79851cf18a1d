#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改面试经验文档昵称为指定的创意昵称
按照文件顺序依次分配昵称，确保每个文件使用不同的昵称
"""

import os
import re
import glob
from pathlib import Path

# 创意昵称列表（完整的200个昵称）
CREATIVE_NICKNAMES = [
    "星河蹦迪官", "月亮邮差", "把风吹进海", "栀子味的晚风", "电量只剩1%",
    "不营业的海豹", "反方向的钟", "加冰乌龙茶", "迷雾派对", "半颗西柚",
    "水泥森林漫游者", "椰汁拿铁", "黑洞系少女", "摆烂研究所长", "格林威治零点",
    "遥控失灵", "行走的句号", "宇宙修字匠", "二氧化浪漫", "零重力啤酒",
    "霓虹折射", "空气曲奇", "迟到的日落", "人间不保固", "熄灯后唱片",
    "锦鲤失眠", "潮汐监听员", "半糖主义", "失语的蓝鲸", "碎片星尘",
    "野生优化器", "逃跑计划B", "世界掉线中", "隐形耳机", "绿光罚站",
    "灵魂小卖部", "沙漏侧写师", "所罗门的懒猫", "厌世薛定谔", "岛屿收音机",
    "风暴预言家", "立体迷宫", "雨水抄送我", "硬盘长蘑菇", "霜降奶盖",
    "植物不打烊", "摇滚星际熊", "射频浪子", "黑夜理疗师", "时间不合格",
    "低压聚合物", "随缘免打扰", "残酷优雅", "云端点火", "后座滤镜",
    "素描月光", "冷启动情书", "零碳恋人", "瞬态信号", "落跑诗集",
    "船票寄错星球", "捕风的乌托邦", "闹钟失约证", "半夏催眠师", "圈养理想",
    "隔壁的麦克风", "银色折叠椅", "凌晨四点半", "雪糕配辣条", "小宇宙灶台",
    "逻辑逃兵", "失重的番茄", "吉他上的海鸥", "玻璃胃骑士", "秘密氧气层",
    "云梯错位", "影子取暖", "洗稿机器人", "斑马线尽头", "碎冰星球",
    "稀释的浪潮", "东风破音", "木星泡泡糖", "一只无框画", "月相备忘录",
    "眯眼的向日葵", "潮汐倒计时", "草莓代码", "暗号收藏家", "漫反射回声",
    "打工失重感", "白噪星屑", "电子榨汁机", "垫脚望远镜", "羽毛收讯台",
    "风很咸", "倒置的日历", "深海录像带", "失联的闹剧", "月光检阅官",
    "龙舌兰过敏", "断电碎碎念", "透明导航仪", "周末失控学", "星球速递",
    "凌迟拖延症", "脾气防沉剂", "码字的萤火虫", "尼龙情绪", "迷路的指北针",
    "上行云层", "预售的温度", "反渗透泡面", "无框限制级", "木质黑匣子",
    "被动结算", "奇想回声壁", "洗手液告白", "频谱巡航", "云走得很慢",
    "一介昙花", "佩奇的朋克", "半成品理想", "檐下光合作用", "零号沙滩",
    "虚焦的镜头", "干杯不负债", "纸短情长", "反义词俱乐部", "时间缝补店",
    "逆行的枕头", "鉴黄师退休", "故障亡命徒", "柠檬电波", "阿尔法泡面",
    "空想逃票者", "夹心月亮", "外卖多巴胺", "供电局诗人", "过期的信封",
    "漫游去火星", "橙色暴走曲", "皮卡丘离线", "潜水式热爱", "押韵的风",
    "省流量星人", "救火队月亮", "暖气片沉思", "球鞋里长草", "纸飞机领航",
    "豆瓣9.9未映", "隔壁宇宙", "闭麦的安魂曲", "蛋黄派失踪", "末班车波段",
    "凹面镜少年", "少女充电桩", "无人贩卖寒冷", "甜度待定", "频闪记忆",
    "月球造浪", "潦草的签名", "心跳速递", "花盆有耳朵", "半导体小熊",
    "弹幕发射口", "克隆星光", "暗夜沙丁鱼", "酒醒导航", "拖线板旅行家",
    "高亮段落", "触电的紫罗兰", "遗忘症备份", "时差漫游", "带刺的气球",
    "卸载后重启", "玻璃心电台", "椰风裂痕", "冰箱学潜水", "漫游的溜溜球",
    "追光溜冰鞋", "小行星许愿池", "乱序的羽毛", "预制孤独", "潜意识打卡",
    "光阴邮筒", "烟火保存剂", "网抑云VIP", "枕边脱氧", "醒着做梦",
    "分形的花瓣", "随机掉线侠", "镜面折叠", "热爱续费中", "光感跳票",
    "起风就碎", "真理打样", "波浪收藏者", "蓝牙耳洞", "无痕模式"
]

def get_all_files_sorted():
    """获取所有面经文件并按文件名排序"""
    all_files = []
    
    # 获取技术岗面经文件
    tech_dir = Path("技术岗面经")
    if tech_dir.exists():
        tech_files = list(tech_dir.glob("*.md"))
        all_files.extend(tech_files)
    
    # 获取非技术岗面经文件
    non_tech_dir = Path("非技术岗面经")
    if non_tech_dir.exists():
        non_tech_files = list(non_tech_dir.glob("*.md"))
        all_files.extend(non_tech_files)
    
    # 按文件名排序（字母数字顺序）
    all_files.sort(key=lambda x: x.name)
    
    return all_files

def update_nickname_in_file(file_path, new_nickname):
    """更新单个文件中的昵称"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换昵称
        nickname_pattern = r'\*\*昵称\*\*：([^\n]*)'
        
        # 替换昵称
        updated_content = re.sub(nickname_pattern, f'**昵称**：{new_nickname}', content)
        
        # 检查是否成功替换
        if updated_content == content:
            print(f"⚠️  警告：{file_path.name} 中未找到昵称字段")
            return False
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始批量修改昵称为指定的创意昵称...")
    
    # 检查昵称列表长度
    if len(CREATIVE_NICKNAMES) < 200:
        print(f"❌ 错误：昵称列表不完整，当前只有 {len(CREATIVE_NICKNAMES)} 个昵称，需要200个")
        print("请提供完整的200个创意昵称列表")
        return
    
    # 获取所有文件并排序
    all_files = get_all_files_sorted()
    
    if len(all_files) != 200:
        print(f"⚠️  警告：找到 {len(all_files)} 个文件，但昵称列表有200个")
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    print("按照文件名字母数字顺序分配昵称...")
    
    success_count = 0
    failed_files = []
    
    # 按顺序分配昵称
    for i, file_path in enumerate(all_files):
        if i < len(CREATIVE_NICKNAMES):
            nickname = CREATIVE_NICKNAMES[i]
            success = update_nickname_in_file(file_path, nickname)
            
            if success:
                success_count += 1
                print(f"✅ [{i+1:3d}] {file_path.name} -> {nickname}")
            else:
                failed_files.append(file_path.name)
        else:
            print(f"⚠️  跳过 {file_path.name}：昵称列表不足")
    
    # 输出结果统计
    print(f"\n" + "="*60)
    print(f"昵称修改完成！")
    print(f"成功处理：{success_count}/{len(all_files)} 个文件")
    print(f"成功率：{success_count/len(all_files)*100:.1f}%")
    
    if failed_files:
        print(f"\n失败的文件：")
        for file_name in failed_files:
            print(f"  - {file_name}")
    
    print(f"\n每个文件都使用了不同的创意昵称，避免了重复")

if __name__ == "__main__":
    main()
