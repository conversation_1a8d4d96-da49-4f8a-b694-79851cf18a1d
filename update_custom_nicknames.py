#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改面试经验文档昵称为指定的创意昵称
按照文件顺序依次分配昵称，确保每个文件使用不同的昵称
"""

import os
import re
import glob
from pathlib import Path

# 创意昵称列表（需要完整的200个昵称）
CREATIVE_NICKNAMES = [
    "星河蹦迪官",
    "月亮邮差", 
    "把风吹进海",
    "栀子味的晚风",
    "电量只剩1%",
    "不营业的海豹",
    "反方向的钟",
    "加冰乌龙茶",
    "迷雾派对",
    "半颗西柚",
    # 这里需要添加剩余的190个昵称
    # 请提供完整的200个昵称列表
]

def get_all_files_sorted():
    """获取所有面经文件并按文件名排序"""
    all_files = []
    
    # 获取技术岗面经文件
    tech_dir = Path("技术岗面经")
    if tech_dir.exists():
        tech_files = list(tech_dir.glob("*.md"))
        all_files.extend(tech_files)
    
    # 获取非技术岗面经文件
    non_tech_dir = Path("非技术岗面经")
    if non_tech_dir.exists():
        non_tech_files = list(non_tech_dir.glob("*.md"))
        all_files.extend(non_tech_files)
    
    # 按文件名排序（字母数字顺序）
    all_files.sort(key=lambda x: x.name)
    
    return all_files

def update_nickname_in_file(file_path, new_nickname):
    """更新单个文件中的昵称"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换昵称
        nickname_pattern = r'\*\*昵称\*\*：([^\n]*)'
        
        # 替换昵称
        updated_content = re.sub(nickname_pattern, f'**昵称**：{new_nickname}', content)
        
        # 检查是否成功替换
        if updated_content == content:
            print(f"⚠️  警告：{file_path.name} 中未找到昵称字段")
            return False
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始批量修改昵称为指定的创意昵称...")
    
    # 检查昵称列表长度
    if len(CREATIVE_NICKNAMES) < 200:
        print(f"❌ 错误：昵称列表不完整，当前只有 {len(CREATIVE_NICKNAMES)} 个昵称，需要200个")
        print("请提供完整的200个创意昵称列表")
        return
    
    # 获取所有文件并排序
    all_files = get_all_files_sorted()
    
    if len(all_files) != 200:
        print(f"⚠️  警告：找到 {len(all_files)} 个文件，但昵称列表有200个")
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    print("按照文件名字母数字顺序分配昵称...")
    
    success_count = 0
    failed_files = []
    
    # 按顺序分配昵称
    for i, file_path in enumerate(all_files):
        if i < len(CREATIVE_NICKNAMES):
            nickname = CREATIVE_NICKNAMES[i]
            success = update_nickname_in_file(file_path, nickname)
            
            if success:
                success_count += 1
                print(f"✅ [{i+1:3d}] {file_path.name} -> {nickname}")
            else:
                failed_files.append(file_path.name)
        else:
            print(f"⚠️  跳过 {file_path.name}：昵称列表不足")
    
    # 输出结果统计
    print(f"\n" + "="*60)
    print(f"昵称修改完成！")
    print(f"成功处理：{success_count}/{len(all_files)} 个文件")
    print(f"成功率：{success_count/len(all_files)*100:.1f}%")
    
    if failed_files:
        print(f"\n失败的文件：")
        for file_name in failed_files:
            print(f"  - {file_name}")
    
    print(f"\n每个文件都使用了不同的创意昵称，避免了重复")

if __name__ == "__main__":
    main()
