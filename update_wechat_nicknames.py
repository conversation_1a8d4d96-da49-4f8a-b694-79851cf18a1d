#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改面试经验文档昵称为微信风格
"""

import os
import re
import random
from pathlib import Path

# 微信风格昵称池（更生活化、个性化）
WECHAT_NICKNAMES = [
    "程序员小哥哥🧑‍💻", "代码女神✨", "Bug终结者🐛", "算法小天才🤖", "前端小仙女🧚‍♀️",
    "后端攻城狮🦁", "全栈大佬💪", "测试小公主👸", "运维小王子🤴", "产品汪🐕",
    "设计师小姐姐🎨", "数据分析师📊", "AI工程师🤖", "云计算专家☁️", "区块链布道者⛓️",
    "移动端开发🔥", "游戏开发者🎮", "安全专家🛡️", "架构师老司机🚗", "技术宅男👨‍💻",
    "编程小能手💻", "代码搬砖工🧱", "算法调参侠⚔️", "前端切图仔✂️", "后端CRUD王👑",
    "测试小蜜蜂🐝", "运营小达人📈", "产品小白兔🐰", "UI设计狮🦁", "UX体验师👁️",
    "Java攻城狮☕", "Python爱好者🐍", "Go语言信徒🐹", "Rust布道师🦀", "C++码农👨‍🌾",
    "JavaScript忍者🥷", "TypeScript战士⚔️", "Vue.js开发者💚", "React工程师⚛️", "Angular专家🅰️",
    "Node.js后端🟢", "Spring Boot达人🍃", "微服务架构师🏗️", "容器化专家🐳", "Kubernetes舵手⛵",
    "大数据工程师📊", "机器学习研究员🧠", "深度学习专家🤖", "计算机视觉👁️", "自然语言处理📝",
    "推荐系统工程师💡", "搜索算法专家🔍", "风控算法师🛡️", "量化交易员📈", "区块链开发者⛓️",
    "游戏引擎工程师🎮", "图形学研究员🎨", "音视频技术🎵", "流媒体开发📺", "直播技术🎬",
    "电商系统架构师🛒", "支付系统专家💳", "物流算法工程师🚚", "地图导航开发🗺️", "出行平台技术🚗",
    "金融科技专家💰", "保险科技工程师🏥", "教育科技开发👨‍🎓", "医疗AI研究员👩‍⚕️", "智能硬件工程师🔧",
    "物联网开发者📡", "边缘计算专家🌐", "5G网络工程师📶", "网络安全专家🔒", "信息安全工程师🛡️",
    "DevOps工程师🔄", "SRE可靠性工程师⚡", "运维自动化🤖", "监控告警专家📊", "性能优化师🚀",
    "数据库管理员💾", "缓存优化专家⚡", "消息队列工程师📮", "分布式系统专家🌐", "高并发架构师🚀",
    "技术Leader👨‍💼", "CTO助理👩‍💼", "技术总监🎯", "首席架构师🏛️", "技术专家🔬",
    "开源贡献者🌟", "技术博主✍️", "会议讲师🎤", "技术顾问💼", "创业技术合伙人🚀",
    "斜杠青年💫", "技术宅🏠", "代码诗人📝", "算法艺术家🎨", "编程哲学家🤔",
    "Bug猎人🏹", "性能杀手⚡", "架构设计师🏗️", "系统优化师🔧", "技术传道者📢",
    "云原生专家☁️", "容器技术达人🐳", "服务网格工程师🕸️", "API设计师🔌", "微前端专家🧩",
    "低代码平台🛠️", "无服务器架构师⚡", "边缘计算开发🌍", "WebAssembly专家🕸️", "PWA开发者📱",
    "跨端开发者🌉", "混合应用专家📱", "小程序开发🔧", "H5游戏开发🎮", "WebGL工程师🎨",
    "AR/VR开发者🥽", "元宇宙工程师🌌", "数字孪生专家👥", "智能合约开发⛓️", "DeFi工程师💰",
    "NFT技术专家🎨", "Web3开发者🌐", "DAO治理专家🏛️", "加密货币工程师💎", "隐私计算专家🔐"
]

def get_random_wechat_nickname():
    """获取随机微信风格昵称"""
    return random.choice(WECHAT_NICKNAMES)

def update_nickname_in_file(file_path):
    """更新单个文件中的昵称"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换昵称
        nickname_pattern = r'\*\*昵称\*\*：([^\n]*)'
        new_nickname = get_random_wechat_nickname()
        
        # 替换昵称
        updated_content = re.sub(nickname_pattern, f'**昵称**：{new_nickname}', content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True, new_nickname
        
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False, None

def main():
    """主函数"""
    print("开始批量修改昵称为微信风格...")
    
    # 获取所有面经文件
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    all_files = []
    if tech_dir.exists():
        all_files.extend(list(tech_dir.glob("*.md")))
    if non_tech_dir.exists():
        all_files.extend(list(non_tech_dir.glob("*.md")))
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    
    success_count = 0
    for file_path in all_files:
        success, new_nickname = update_nickname_in_file(file_path)
        if success:
            success_count += 1
            print(f"✅ {file_path.name} -> {new_nickname}")
    
    print(f"\n昵称修改完成！成功处理 {success_count}/{len(all_files)} 个文件")

if __name__ == "__main__":
    main()
