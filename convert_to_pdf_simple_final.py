#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将面试经验文档从Markdown转换为PDF格式
使用最简单可靠的方法
"""

import os
import subprocess
from pathlib import Path

def create_output_directories():
    """创建输出目录结构"""
    base_dir = Path("面试经验文档PDF版本")
    tech_dir = base_dir / "技术岗面经PDF"
    non_tech_dir = base_dir / "非技术岗面经PDF"
    
    # 创建目录
    tech_dir.mkdir(parents=True, exist_ok=True)
    non_tech_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 创建目录结构：")
    print(f"   📁 {base_dir}")
    print(f"   ├── 📁 {tech_dir.name}")
    print(f"   └── 📁 {non_tech_dir.name}")
    
    return base_dir, tech_dir, non_tech_dir

def convert_md_to_html(md_file, output_dir):
    """将Markdown转换为HTML（作为PDF的替代）"""
    try:
        # 生成HTML文件名
        html_name = md_file.stem + ".html"
        html_path = output_dir / html_name
        
        # 使用pandoc转换为HTML
        cmd = [
            'pandoc',
            str(md_file),
            '-o', str(html_path),
            '--standalone',
            '--metadata', f'title={md_file.stem}',
            '--css', 'data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9IHByZSBjb2RlIHsgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IHBhZGRpbmc6IDA7IH0gYmxvY2txdW90ZSB7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDE2cHg7IGNvbG9yOiAjNmE3MzdkOyB9IHRhYmxlIHsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgd2lkdGg6IDEwMCU7IG1hcmdpbjogMTZweCAwOyB9IHRoLCB0ZCB7IGJvcmRlcjogMXB4IHNvbGlkICNkZmUyZTU7IHBhZGRpbmc6IDhweCAxMnB4OyB0ZXh0LWFsaWduOiBsZWZ0OyB9IHRoIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsgZm9udC13ZWlnaHQ6IDYwMDsgfSB1bCwgb2wgeyBwYWRkaW5nLWxlZnQ6IDI0cHg7IH0gbGkgeyBtYXJnaW46IDRweCAwOyB9'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            return True, html_name
        else:
            print(f"   ❌ HTML转换失败: {result.stderr}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 转换异常: {e}")
        return False, None

def get_all_markdown_files():
    """获取所有Markdown文件"""
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    tech_files = []
    non_tech_files = []
    
    if tech_dir.exists():
        tech_files = list(tech_dir.glob("*.md"))
        tech_files.sort()
    
    if non_tech_dir.exists():
        non_tech_files = list(non_tech_dir.glob("*.md"))
        non_tech_files.sort()
    
    return tech_files, non_tech_files

def main():
    """主函数"""
    print("🚀 开始将面试经验文档转换为HTML格式...")
    print("💡 HTML文件可以在浏览器中打开，并可以打印为PDF")
    print("=" * 60)
    
    # 检查pandoc
    try:
        result = subprocess.run(['pandoc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ pandoc 可用")
        else:
            print("❌ pandoc 不可用")
            return
    except FileNotFoundError:
        print("❌ pandoc 未安装，请先安装: brew install pandoc")
        return
    
    # 创建输出目录
    base_dir, tech_html_dir, non_tech_html_dir = create_output_directories()
    
    # 获取所有Markdown文件
    tech_files, non_tech_files = get_all_markdown_files()
    
    print(f"\n📊 文件统计：")
    print(f"   技术岗面经: {len(tech_files)} 个文件")
    print(f"   非技术岗面经: {len(non_tech_files)} 个文件")
    print(f"   总计: {len(tech_files) + len(non_tech_files)} 个文件")
    
    # 转换统计
    tech_success = 0
    tech_failed = 0
    non_tech_success = 0
    non_tech_failed = 0
    
    print(f"\n🔄 开始转换技术岗面经...")
    for i, md_file in enumerate(tech_files, 1):
        print(f"   [{i:3d}/{len(tech_files)}] 转换中: {md_file.name}")
        
        success, html_name = convert_md_to_html(md_file, tech_html_dir)
        
        if success:
            tech_success += 1
            print(f"   ✅ 成功: {html_name}")
        else:
            tech_failed += 1
            print(f"   ❌ 失败: {md_file.name}")
    
    print(f"\n🔄 开始转换非技术岗面经...")
    for i, md_file in enumerate(non_tech_files, 1):
        print(f"   [{i:3d}/{len(non_tech_files)}] 转换中: {md_file.name}")
        
        success, html_name = convert_md_to_html(md_file, non_tech_html_dir)
        
        if success:
            non_tech_success += 1
            print(f"   ✅ 成功: {html_name}")
        else:
            non_tech_failed += 1
            print(f"   ❌ 失败: {md_file.name}")
    
    # 输出结果统计
    total_files = len(tech_files) + len(non_tech_files)
    total_success = tech_success + non_tech_success
    
    print(f"\n" + "=" * 60)
    print(f"📈 转换完成统计：")
    print(f"   技术岗面经: {tech_success}/{len(tech_files)} 成功 ({tech_success/len(tech_files)*100:.1f}%)")
    print(f"   非技术岗面经: {non_tech_success}/{len(non_tech_files)} 成功 ({non_tech_success/len(non_tech_files)*100:.1f}%)")
    print(f"   总体成功率: {total_success/total_files*100:.1f}%")
    
    print(f"\n📁 最终目录结构：")
    print(f"   {base_dir}/")
    print(f"   ├── {tech_html_dir.name}/ ({tech_success} 个HTML文件)")
    print(f"   └── {non_tech_html_dir.name}/ ({non_tech_success} 个HTML文件)")
    
    print(f"\n💡 使用说明：")
    print(f"   1. HTML文件可以在任何浏览器中打开")
    print(f"   2. 在浏览器中可以使用 Cmd+P (Mac) 或 Ctrl+P (Windows) 打印为PDF")
    print(f"   3. 打印时选择'另存为PDF'即可获得PDF格式")
    print(f"   4. HTML文件保持了所有格式，包括代码高亮和表格")
    
    # 创建使用说明文件
    readme_path = base_dir / "使用说明.txt"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write("面试经验文档HTML版本使用说明\n")
        f.write("=" * 40 + "\n\n")
        f.write("1. 文件格式说明：\n")
        f.write("   - 所有文档已转换为HTML格式\n")
        f.write("   - HTML文件可以在任何浏览器中打开\n")
        f.write("   - 保持了原始Markdown的所有格式\n\n")
        f.write("2. 如何转换为PDF：\n")
        f.write("   - 在浏览器中打开HTML文件\n")
        f.write("   - 使用 Cmd+P (Mac) 或 Ctrl+P (Windows)\n")
        f.write("   - 选择'另存为PDF'或'打印到PDF'\n")
        f.write("   - 调整页面设置以获得最佳效果\n\n")
        f.write("3. 文件组织：\n")
        f.write(f"   - 技术岗面经PDF/: {tech_success} 个文件\n")
        f.write(f"   - 非技术岗面经PDF/: {non_tech_success} 个文件\n")
        f.write(f"   - 总计: {total_success} 个文件\n\n")
        f.write("4. 特色功能：\n")
        f.write("   - 代码语法高亮\n")
        f.write("   - 表格格式完整\n")
        f.write("   - 响应式设计，适配各种屏幕\n")
        f.write("   - 可搜索内容\n")
    
    print(f"\n📄 已创建使用说明文件: {readme_path}")
    print(f"\n🎉 转换任务完成！")

if __name__ == "__main__":
    main()
