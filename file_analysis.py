#!/usr/bin/env python3
import os
import re

def analyze_directory(directory_path, directory_name):
    print(f"\n{'='*60}")
    print(f"ANALYSIS FOR: {directory_name}")
    print(f"{'='*60}")
    
    files = []
    if os.path.exists(directory_path):
        files = [f for f in os.listdir(directory_path) if f.endswith('.md')]
    
    # Extract numbers and sort
    file_numbers = []
    for file in files:
        match = re.match(r'^(\d+)\.', file)
        if match:
            num = int(match.group(1))
            file_numbers.append((num, file))
    
    file_numbers.sort(key=lambda x: x[0])
    
    # Find duplicates
    numbers_only = [num for num, _ in file_numbers]
    duplicates = {}
    for num in numbers_only:
        count = numbers_only.count(num)
        if count > 1:
            duplicates[num] = count
    
    # Find missing numbers
    if file_numbers:
        max_num = max(numbers_only)
        expected_range = set(range(1, max_num + 1))
        actual_numbers = set(numbers_only)
        missing = sorted(expected_range - actual_numbers)
    else:
        missing = []
    
    # Find out-of-sequence numbers
    out_of_sequence = []
    for i, (num, file) in enumerate(file_numbers):
        if num > 99 and i < len(file_numbers) - 1:  # 100 appears before end
            out_of_sequence.append((num, file))
    
    # Report findings
    print(f"Total files: {len(files)}")
    print(f"Numbered files: {len(file_numbers)}")
    print(f"Number range: {min(numbers_only) if numbers_only else 'N/A'} - {max(numbers_only) if numbers_only else 'N/A'}")
    
    if duplicates:
        print(f"\n❌ DUPLICATE NUMBERS FOUND:")
        for num, count in duplicates.items():
            print(f"   Number {num:02d}: appears {count} times")
            duplicate_files = [file for n, file in file_numbers if n == num]
            for file in duplicate_files:
                print(f"     - {file}")
    
    if missing:
        print(f"\n❌ MISSING NUMBERS:")
        print(f"   {', '.join([str(n).zfill(2) for n in missing])}")
    
    if out_of_sequence:
        print(f"\n⚠️  OUT-OF-SEQUENCE NUMBERS:")
        for num, file in out_of_sequence:
            print(f"   {num:03d}: {file}")
    
    # Check for potential content duplicates by filename similarity
    companies = {}
    for num, file in file_numbers:
        # Extract company name (part after number and before dash)
        match = re.search(r'^\d+\.\s*([^-]+)', file)
        if match:
            company = match.group(1).strip()
            if company not in companies:
                companies[company] = []
            companies[company].append((num, file))
    
    potential_duplicates = {k: v for k, v in companies.items() if len(v) > 1}
    if potential_duplicates:
        print(f"\n⚠️  POTENTIAL CONTENT DUPLICATES (same company):")
        for company, files_list in potential_duplicates.items():
            print(f"   {company}:")
            for num, file in files_list:
                print(f"     {num:02d}: {file}")
    
    # Summary
    if not duplicates and not missing and not out_of_sequence:
        print(f"\n✅ NO NUMBERING ISSUES FOUND")
    else:
        issues_count = len(duplicates) + len(missing) + len(out_of_sequence)
        print(f"\n📊 SUMMARY: {issues_count} numbering issues found")
    
    return {
        'duplicates': duplicates,
        'missing': missing,
        'out_of_sequence': out_of_sequence,
        'potential_content_duplicates': potential_duplicates
    }

# Main analysis
base_path = "/Users/<USER>/Desktop/龙井面经"
tech_path = os.path.join(base_path, "技术岗面经")
non_tech_path = os.path.join(base_path, "非技术岗面经")

print("FILE NUMBERING ANALYSIS REPORT")
print("=" * 80)

tech_results = analyze_directory(tech_path, "技术岗面经")
non_tech_results = analyze_directory(non_tech_path, "非技术岗面经")

# Cross-directory analysis
print(f"\n{'='*60}")
print("CROSS-DIRECTORY ANALYSIS")
print(f"{'='*60}")

total_duplicates = len(tech_results['duplicates']) + len(non_tech_results['duplicates'])
total_missing = len(tech_results['missing']) + len(non_tech_results['missing'])
total_out_of_sequence = len(tech_results['out_of_sequence']) + len(non_tech_results['out_of_sequence'])

print(f"Total duplicate numbers: {total_duplicates}")
print(f"Total missing numbers: {total_missing}")  
print(f"Total out-of-sequence numbers: {total_out_of_sequence}")

if total_duplicates == 0 and total_missing == 0 and total_out_of_sequence == 0:
    print("\n✅ ALL DIRECTORIES HAVE CONSISTENT NUMBERING")
else:
    print(f"\n❌ TOTAL ISSUES ACROSS BOTH DIRECTORIES: {total_duplicates + total_missing + total_out_of_sequence}")

print(f"\n{'='*60}")
print("RECOMMENDED ACTIONS")
print(f"{'='*60}")

if tech_results['duplicates']:
    print("1. Resolve duplicate number 61 in 技术岗面经:")
    print("   - Rename one file to number 67 (the missing number)")
    
if tech_results['missing']:
    print("2. Fill missing number gaps in 技术岗面经")

if tech_results['out_of_sequence'] or non_tech_results['out_of_sequence']:
    print("3. Move number 100 files to proper sequence or rename")
    print("   - Consider renaming to next available numbers in sequence")