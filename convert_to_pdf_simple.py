#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将面试经验文档从Markdown转换为PDF格式
使用Python库进行转换，无需外部依赖
"""

import os
import sys
from pathlib import Path
import subprocess

def install_required_packages():
    """安装必要的Python包"""
    packages = ['markdown', 'pdfkit', 'weasyprint']
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

def create_output_directories():
    """创建输出目录结构"""
    base_dir = Path("面试经验文档PDF版本")
    tech_dir = base_dir / "技术岗面经PDF"
    non_tech_dir = base_dir / "非技术岗面经PDF"
    
    # 创建目录
    tech_dir.mkdir(parents=True, exist_ok=True)
    non_tech_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 创建目录结构：")
    print(f"   📁 {base_dir}")
    print(f"   ├── 📁 {tech_dir.name}")
    print(f"   └── 📁 {non_tech_dir.name}")
    
    return base_dir, tech_dir, non_tech_dir

def convert_md_to_pdf_weasyprint(md_file, output_dir):
    """使用WeasyPrint将Markdown转换为PDF"""
    try:
        import markdown
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration
        
        # 读取Markdown文件
        with open(md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换为HTML
        md = markdown.Markdown(extensions=['codehilite', 'tables', 'toc'])
        html_content = md.convert(md_content)
        
        # 添加CSS样式
        css_style = """
        <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 16px;
        }
        h1 { font-size: 28px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { font-size: 24px; border-bottom: 1px solid #bdc3c7; padding-bottom: 8px; }
        h3 { font-size: 20px; }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            overflow-x: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 0;
            padding-left: 16px;
            color: #6a737d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        ul, ol {
            padding-left: 24px;
        }
        li {
            margin: 4px 0;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        </style>
        """
        
        # 完整的HTML文档
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{md_file.stem}</title>
            {css_style}
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """
        
        # 生成PDF
        pdf_name = md_file.stem + ".pdf"
        pdf_path = output_dir / pdf_name
        
        # 使用WeasyPrint转换
        HTML(string=full_html).write_pdf(str(pdf_path))
        
        return True, pdf_name
        
    except Exception as e:
        print(f"❌ 转换失败: {md_file.name} - {e}")
        return False, None

def convert_md_to_pdf_pandoc(md_file, output_dir):
    """使用pandoc转换（备用方案）"""
    try:
        pdf_name = md_file.stem + ".pdf"
        pdf_path = output_dir / pdf_name
        
        cmd = [
            'pandoc',
            str(md_file),
            '-o', str(pdf_path),
            '--highlight-style=github',
            '--toc',
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            return True, pdf_name
        else:
            return False, None
            
    except Exception as e:
        return False, None

def get_all_markdown_files():
    """获取所有Markdown文件"""
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    tech_files = []
    non_tech_files = []
    
    if tech_dir.exists():
        tech_files = list(tech_dir.glob("*.md"))
        tech_files.sort()
    
    if non_tech_dir.exists():
        non_tech_files = list(non_tech_dir.glob("*.md"))
        non_tech_files.sort()
    
    return tech_files, non_tech_files

def main():
    """主函数"""
    print("🚀 开始将面试经验文档转换为PDF格式...")
    print("=" * 60)
    
    # 安装必要的包
    print("📦 检查并安装必要的Python包...")
    install_required_packages()
    
    # 创建输出目录
    base_dir, tech_pdf_dir, non_tech_pdf_dir = create_output_directories()
    
    # 获取所有Markdown文件
    tech_files, non_tech_files = get_all_markdown_files()
    
    print(f"\n📊 文件统计：")
    print(f"   技术岗面经: {len(tech_files)} 个文件")
    print(f"   非技术岗面经: {len(non_tech_files)} 个文件")
    print(f"   总计: {len(tech_files) + len(non_tech_files)} 个文件")
    
    # 转换统计
    tech_success = 0
    tech_failed = 0
    non_tech_success = 0
    non_tech_failed = 0
    
    print(f"\n🔄 开始转换技术岗面经...")
    for i, md_file in enumerate(tech_files, 1):
        print(f"   [{i:3d}/{len(tech_files)}] 转换中: {md_file.name}")
        
        # 首先尝试WeasyPrint
        success, pdf_name = convert_md_to_pdf_weasyprint(md_file, tech_pdf_dir)
        
        # 如果失败，尝试pandoc
        if not success:
            success, pdf_name = convert_md_to_pdf_pandoc(md_file, tech_pdf_dir)
        
        if success:
            tech_success += 1
            print(f"   ✅ 成功: {pdf_name}")
        else:
            tech_failed += 1
            print(f"   ❌ 失败: {md_file.name}")
    
    print(f"\n🔄 开始转换非技术岗面经...")
    for i, md_file in enumerate(non_tech_files, 1):
        print(f"   [{i:3d}/{len(non_tech_files)}] 转换中: {md_file.name}")
        
        # 首先尝试WeasyPrint
        success, pdf_name = convert_md_to_pdf_weasyprint(md_file, non_tech_pdf_dir)
        
        # 如果失败，尝试pandoc
        if not success:
            success, pdf_name = convert_md_to_pdf_pandoc(md_file, non_tech_pdf_dir)
        
        if success:
            non_tech_success += 1
            print(f"   ✅ 成功: {pdf_name}")
        else:
            non_tech_failed += 1
            print(f"   ❌ 失败: {md_file.name}")
    
    # 输出结果统计
    total_files = len(tech_files) + len(non_tech_files)
    total_success = tech_success + non_tech_success
    
    print(f"\n" + "=" * 60)
    print(f"📈 转换完成统计：")
    print(f"   技术岗面经: {tech_success}/{len(tech_files)} 成功 ({tech_success/len(tech_files)*100:.1f}%)")
    print(f"   非技术岗面经: {non_tech_success}/{len(non_tech_files)} 成功 ({non_tech_success/len(non_tech_files)*100:.1f}%)")
    print(f"   总体成功率: {total_success/total_files*100:.1f}%")
    
    print(f"\n📁 输出目录结构：")
    print(f"   {base_dir}/")
    print(f"   ├── {tech_pdf_dir.name}/ ({tech_success} 个PDF文件)")
    print(f"   └── {non_tech_pdf_dir.name}/ ({non_tech_success} 个PDF文件)")
    
    print(f"\n🎉 PDF转换任务完成！")

if __name__ == "__main__":
    main()
