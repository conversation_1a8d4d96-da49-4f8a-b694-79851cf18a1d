#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面经文件一致性验证报告
检查所有文件的格式、结构、字数等一致性
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class InterviewFileVerifier:
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.tech_dir = self.base_dir / "技术岗面经"
        self.non_tech_dir = self.base_dir / "非技术岗面经"
        
    def analyze_file_structure(self, file_path: Path) -> Dict:
        """分析单个文件的结构"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分析各项指标
            analysis = {
                'file_name': file_path.name,
                'total_chars': len(content),
                'total_lines': len(content.split('\n')),
                'has_title': bool(re.search(r'^# .+', content, re.MULTILINE)),
                'has_author_section': bool(re.search(r'## 👤 作者信息', content)),
                'has_background_section': bool(re.search(r'## 📝 个人背景', content)),
                'has_interview_section': bool(re.search(r'## 🔍 面试流程', content)),
                'has_result_section': bool(re.search(r'## 📊 面试结果', content)),
                'has_summary_section': bool(re.search(r'## 💡 经验总结', content)),
                'emoji_count': len(re.findall(r'[👤📝🔍📊💡]', content)),
                'section_count': len(re.findall(r'^## ', content, re.MULTILINE)),
                'placeholder_count': len(re.findall(r'\[.*?\]', content))
            }
            
            return analysis
            
        except Exception as e:
            return {'file_name': file_path.name, 'error': str(e)}

    def generate_report(self) -> Dict:
        """生成完整的验证报告"""
        report = {
            'tech_files': [],
            'non_tech_files': [],
            'summary': {}
        }
        
        # 分析技术岗文件
        for file_path in sorted(self.tech_dir.glob("*.md")):
            analysis = self.analyze_file_structure(file_path)
            report['tech_files'].append(analysis)
        
        # 分析非技术岗文件
        for file_path in sorted(self.non_tech_dir.glob("*.md")):
            analysis = self.analyze_file_structure(file_path)
            report['non_tech_files'].append(analysis)
        
        # 生成统计摘要
        all_files = report['tech_files'] + report['non_tech_files']
        valid_files = [f for f in all_files if 'error' not in f]
        
        if valid_files:
            report['summary'] = {
                'total_files': len(all_files),
                'valid_files': len(valid_files),
                'avg_chars': sum(f['total_chars'] for f in valid_files) / len(valid_files),
                'avg_lines': sum(f['total_lines'] for f in valid_files) / len(valid_files),
                'structure_compliance': {
                    'has_title': sum(f['has_title'] for f in valid_files),
                    'has_author': sum(f['has_author_section'] for f in valid_files),
                    'has_background': sum(f['has_background_section'] for f in valid_files),
                    'has_interview': sum(f['has_interview_section'] for f in valid_files),
                    'has_result': sum(f['has_result_section'] for f in valid_files),
                    'has_summary': sum(f['has_summary_section'] for f in valid_files)
                },
                'placeholder_files': len([f for f in valid_files if f['placeholder_count'] > 0])
            }
        
        return report

    def print_report(self):
        """打印格式化的验证报告"""
        report = self.generate_report()
        
        print("📋 面经文件格式验证报告")
        print("=" * 50)
        
        summary = report['summary']
        print(f"\n📊 总体统计:")
        print(f"总文件数: {summary['total_files']}")
        print(f"有效文件数: {summary['valid_files']}")
        print(f"平均字数: {summary['avg_chars']:.0f}")
        print(f"平均行数: {summary['avg_lines']:.0f}")
        
        print(f"\n📐 结构一致性:")
        structure = summary['structure_compliance']
        total = summary['valid_files']
        print(f"标题格式: {structure['has_title']}/{total} ({structure['has_title']/total*100:.1f}%)")
        print(f"作者信息: {structure['has_author']}/{total} ({structure['has_author']/total*100:.1f}%)")
        print(f"个人背景: {structure['has_background']}/{total} ({structure['has_background']/total*100:.1f}%)")
        print(f"面试流程: {structure['has_interview']}/{total} ({structure['has_interview']/total*100:.1f}%)")
        print(f"面试结果: {structure['has_result']}/{total} ({structure['has_result']/total*100:.1f}%)")
        print(f"经验总结: {structure['has_summary']}/{total} ({structure['has_summary']/total*100:.1f}%)")
        
        print(f"\n⚠️  待完善文件:")
        print(f"包含占位符的文件: {summary['placeholder_files']}")
        
        # 字数分布统计
        valid_files = [f for f in report['tech_files'] + report['non_tech_files'] if 'error' not in f]
        char_counts = [f['total_chars'] for f in valid_files]
        
        print(f"\n📈 字数分布:")
        print(f"最少字数: {min(char_counts)}")
        print(f"最多字数: {max(char_counts)}")
        print(f"中位数: {sorted(char_counts)[len(char_counts)//2]}")
        
        # 找出需要改进的文件
        short_files = [f for f in valid_files if f['total_chars'] < 500]
        long_files = [f for f in valid_files if f['total_chars'] > 3000]
        
        if short_files:
            print(f"\n⚠️  字数较少的文件 (<500字):")
            for f in short_files[:5]:  # 显示前5个
                print(f"  - {f['file_name']}: {f['total_chars']}字")
        
        if long_files:
            print(f"\n⚠️  字数较多的文件 (>3000字):")
            for f in long_files[:5]:  # 显示前5个
                print(f"  - {f['file_name']}: {f['total_chars']}字")
        
        # 文件编号检查
        print(f"\n🔢 文件编号检查:")
        tech_numbers = []
        non_tech_numbers = []
        
        for f in report['tech_files']:
            if 'error' not in f:
                match = re.match(r'^(\d+)\.', f['file_name'])
                if match:
                    tech_numbers.append(int(match.group(1)))
        
        for f in report['non_tech_files']:
            if 'error' not in f:
                match = re.match(r'^(\d+)\.', f['file_name'])
                if match:
                    non_tech_numbers.append(int(match.group(1)))
        
        tech_missing = set(range(1, 101)) - set(tech_numbers)
        non_tech_missing = set(range(1, 101)) - set(non_tech_numbers)
        
        if tech_missing:
            print(f"技术岗缺失编号: {sorted(tech_missing)}")
        else:
            print("技术岗编号完整 ✅")
            
        if non_tech_missing:
            print(f"非技术岗缺失编号: {sorted(non_tech_missing)}")
        else:
            print("非技术岗编号完整 ✅")
        
        print(f"\n✅ 标准化完成度评估:")
        compliance_rate = (
            structure['has_title'] + structure['has_author'] + 
            structure['has_background'] + structure['has_interview'] + 
            structure['has_result'] + structure['has_summary']
        ) / (6 * total) * 100
        
        print(f"整体标准化完成度: {compliance_rate:.1f}%")
        
        if compliance_rate >= 95:
            print("🎉 标准化质量优秀！")
        elif compliance_rate >= 85:
            print("👍 标准化质量良好")
        else:
            print("⚠️  需要进一步优化")

def main():
    base_dir = "/Users/<USER>/Desktop/龙井面经"
    verifier = InterviewFileVerifier(base_dir)
    verifier.print_report()

if __name__ == "__main__":
    main()