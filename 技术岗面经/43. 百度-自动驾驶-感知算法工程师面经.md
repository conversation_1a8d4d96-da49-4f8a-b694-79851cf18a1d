# 百度-自动驾驶-感知算法工程师面经

## 作者信息
**昵称**：HardwareHacker
**背景**：硕士毕业，计算机视觉专业，有4年感知算法开发经验。熟悉深度学习、计算机视觉和自动驾驶感知技术，对Apollo平台有深入了解。
**面试结果**：通过

## 个人背景介绍

我硕士学的计算机视觉，研究方向是目标检测和语义分割。毕业后在一家自动驾驶公司做感知算法工程师，主要负责视觉感知算法的开发和优化。

我们公司的自动驾驶系统已经在多个城市进行路测，我主要负责：
1. 目标检测算法开发，使用YOLO、SSD等网络架构
2. 语义分割算法优化，实现道路、车辆、行人的精确分割
3. 多传感器融合，结合摄像头、激光雷达、毫米波雷达数据
4. 算法工程化部署，在车载计算平台上实现实时推理

最有成就感的项目是优化了车辆检测算法，通过数据增强、网络结构改进和后处理优化，将检测精度从92%提升到96%，同时推理速度提升了30%。

选择百度主要是被Apollo平台的技术实力吸引。百度在自动驾驶领域起步较早，技术积累深厚，特别是在感知、定位、规划等核心技术方面都有领先优势。我希望能在这样的平台上，参与更大规模的自动驾驶系统开发。

## 面试准备过程

收到面试通知后，我花了4周时间做深入准备：

**计算机视觉技术深化**：
- 深入学习了最新的目标检测算法，如YOLOX、DETR、Swin Transformer等
- 研究了语义分割的前沿技术，如SegFormer、Mask2Former等
- 学习了3D目标检测和BEV感知的相关技术

**自动驾驶感知技术**：
- 深入了解了Apollo感知模块的技术架构和算法实现
- 研究了多传感器融合的技术方案和工程实践
- 学习了端到端感知算法的最新进展

**深度学习框架**：
- 深入学习了PaddlePaddle框架，这是百度主推的深度学习框架
- 研究了模型压缩、量化、蒸馏等模型优化技术
- 学习了TensorRT、OpenVINO等推理加速框架

**项目案例准备**：
- 准备了5个核心感知算法项目的详细技术方案
- 整理了算法优化、工程部署、性能调优的具体案例
- 总结了多传感器融合和算法工程化的经验

## 面试流程详细描述

### 一面（计算机视觉基础面试 - 75分钟）
面试官是百度自动驾驶感知团队的资深算法工程师，主要考察计算机视觉和深度学习基础。面试在百度科技园进行，环境很现代化。

**深度学习基础考察**
面试官先问了一些深度学习的核心问题：

"说说卷积神经网络的基本原理？"
我从卷积操作、池化、激活函数开始，详细解释了CNN的前向传播和反向传播过程，还提到了不同卷积类型（标准卷积、深度可分离卷积、空洞卷积）的特点。

"Batch Normalization的作用和原理？"
我解释了BN解决内部协变量偏移的问题，加速训练收敛，还对比了Layer Norm、Group Norm等其他归一化方法的差异。

"目标检测算法的发展历程？"
我从两阶段检测器（R-CNN、Fast R-CNN、Faster R-CNN）讲到单阶段检测器（YOLO、SSD），再到最新的Transformer-based方法（DETR、Deformable DETR），分析了各自的优缺点。

**计算机视觉专业问题**
"如何处理目标检测中的多尺度问题？"
我回答："主要有几种方法：1. 特征金字塔网络（FPN）；2. 多尺度训练和测试；3. 空洞卷积增大感受野；4. 不同尺度的anchor设计。在实际项目中，我们使用了FPN+多尺度训练的组合方案。"

"语义分割和实例分割的区别？"
我详细对比了两者的定义、应用场景和技术实现，还提到了全景分割这个更复杂的任务。

**自动驾驶感知经验**
我分享了车辆检测项目的技术细节：
"我们的车辆检测系统需要在各种天气和光照条件下稳定工作。我采用了YOLOv5作为基础架构，针对车载场景做了几个关键优化：

1. 数据增强：使用Mosaic、MixUp、CutMix等技术增强模型泛化能力
2. 网络改进：引入注意力机制（CBAM）提升特征表达能力
3. 损失函数优化：使用Focal Loss解决正负样本不平衡问题
4. 后处理优化：改进NMS算法，减少漏检和误检"

**编程题**
题目是"实现非极大值抑制（NMS）算法"，我用Python实现了完整版本：

```python
import numpy as np

def nms(boxes, scores, iou_threshold):
    """
    非极大值抑制算法
    Args:
        boxes: 边界框坐标 (N, 4) [x1, y1, x2, y2]
        scores: 置信度分数 (N,)
        iou_threshold: IoU阈值
    Returns:
        keep: 保留的边界框索引
    """
    # 计算边界框面积
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

    # 按置信度降序排序
    order = scores.argsort()[::-1]

    keep = []
    while order.size > 0:
        # 保留置信度最高的框
        i = order[0]
        keep.append(i)

        # 计算IoU
        xx1 = np.maximum(boxes[i, 0], boxes[order[1:], 0])
        yy1 = np.maximum(boxes[i, 1], boxes[order[1:], 1])
        xx2 = np.minimum(boxes[i, 2], boxes[order[1:], 2])
        yy2 = np.minimum(boxes[i, 3], boxes[order[1:], 3])

        w = np.maximum(0, xx2 - xx1)
        h = np.maximum(0, yy2 - yy1)
        intersection = w * h

        iou = intersection / (areas[i] + areas[order[1:]] - intersection)

        # 保留IoU小于阈值的框
        inds = np.where(iou <= iou_threshold)[0]
        order = order[inds + 1]

    return keep
```

面试官对我的实现很满意，还问了Soft-NMS和DIoU-NMS等改进算法的原理。

### 二面（技术深度面试 - 75分钟）
面试官是自动驾驶的技术专家，重点考察技术深度和系统设计能力。

**系统设计**
- 设计一个高并发的感知算法工程师系统
- 考虑可扩展性、可用性、一致性等技术指标
- 数据库设计、缓存策略、消息队列等技术选型
- 监控告警、降级熔断等保障措施

**技术深度挖掘**
- 深入讨论使用过的技术栈的底层原理
- 性能优化的具体实践和效果
- 技术难点的解决思路和方案对比
- 技术趋势的理解和前瞻性思考

我详细设计了一个完整的系统架构，从前端到后端、从存储到网络的全链路技术方案，面试官对设计思路比较满意。

### 三面（综合能力面试 - 60分钟）
面试官是部门负责人，考察综合素质和发展潜力。

**技术视野**
- 对行业技术发展趋势的看法
- 新技术的学习方法和实践经验
- 技术选型的决策思路和评估标准
- 团队技术管理和代码质量保证

**项目管理**
- 项目开发流程和质量保证
- 团队协作和沟通协调能力
- 技术债务管理和重构经验
- 跨部门合作的实践经验

**学习成长**
- 技术学习路径和成长规划
- 遇到技术瓶颈时的突破方法
- 在百度的职业发展期望
- 对自动驾驶业务的理解和想法

面试官比较认可我的技术深度和学习能力，特别是对感知算法工程师领域的理解和规划。

### 四面（HR面试 - 30分钟）
HR面试主要了解个人情况和求职动机。

- 为什么选择百度和自动驾驶
- 职业规划和发展期望
- 薪资期望和福利关注点
- 工作节奏和压力承受能力
- 入职时间和其他面试进展

整体沟通很顺畅，HR对我的背景和动机比较认可。

## 面试结果

面试过程很专业，最终成功拿到offer。对未来的工作很期待。

## 经验总结

**技术准备建议：**
1. **基础要扎实**：技术能力，项目经验，学习能力，团队合作等核心能力必须过关
2. **项目要深入**：准备2-3个有深度的项目案例，能讲清楚技术方案和思考过程
3. **视野要开阔**：关注技术发展趋势，有自己的思考和见解
4. **表达要清晰**：技术方案要能清楚表达，逻辑性要强

**面试技巧方面：**
1. **准备要充分**：提前了解公司技术栈和业务特点
2. **回答要结构化**：先总结后展开，逻辑清晰
3. **态度要诚恳**：不会的坦诚说明，展现学习意愿
4. **互动要积极**：主动提问，展现对公司和岗位的兴趣

百度的面试整体很专业，注重技术实力的同时也看重学习能力和发展潜力。建议准备面试的同学重点关注技术深度和项目经验，同时要展现出持续学习的能力和对技术的热情。

希望这个面经能对准备感知算法工程师岗位面试的同学有所帮助！