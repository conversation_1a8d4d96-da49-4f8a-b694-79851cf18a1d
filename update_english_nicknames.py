#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改面试经验文档昵称为英文风格
"""

import os
import re
import random
from pathlib import Path

# 英文风格昵称池（专业、简洁、国际化）
ENGLISH_NICKNAMES = [
    "CodeMaster", "TechGuru", "DevNinja", "AlgoWizard", "FullStackDev",
    "BackendPro", "FrontendExpert", "DataScientist", "MLEngineer", "AIResearcher",
    "CloudArchitect", "DevOpsEngineer", "SREExpert", "SecuritySpecialist", "BlockchainDev",
    "MobileDevPro", "GameDeveloper", "UIUXDesigner", "ProductManager", "TechLead",
    "SoftwareEngineer", "SystemArchitect", "DatabaseAdmin", "NetworkEngineer", "QAEngineer",
    "JavaDeveloper", "PythonCoder", "GoEnthusiast", "RustDeveloper", "CppProgrammer",
    "JSNinja", "ReactDev", "VueMaster", "AngularPro", "NodejsExpert",
    "SpringBootDev", "DjangoMaster", "FlaskCoder", "ExpressExpert", "FastAPIDev",
    "DockerPro", "KubernetesGuru", "AWSExpert", "AzureSpecialist", "GCPDeveloper",
    "BigDataEngineer", "DataAnalyst", "BusinessAnalyst", "ProductOwner", "ScrumMaster",
    "TechWriter", "DevAdvocate", "OpenSourceDev", "StartupCTO", "TechConsultant",
    "CodeReviewer", "PerformanceTuner", "SecurityAuditor", "PenetrationTester", "CyberSecPro",
    "IoTDeveloper", "EmbeddedPro", "FirmwareEngineer", "HardwareHacker", "RoboticsEngineer",
    "VisionEngineer", "NLPExpert", "DeepLearningPro", "ReinforcementLearning", "ComputerVision",
    "SearchEngineer", "RecommendationSys", "AdTechExpert", "FinTechDev", "HealthTechPro",
    "EdTechEngineer", "GamingTechPro", "MediaTechExpert", "StreamingTechDev", "LiveTechPro",
    "EcommerceDev", "PaymentSysExpert", "LogisticsTechPro", "SupplyChainTech", "RetailTechDev",
    "AutoTechEngineer", "AutonomousDriving", "SmartCarDev", "TransportationTech", "MobilityExpert",
    "QuantDeveloper", "TradingSystemDev", "RiskManagementTech", "InsurTechPro", "WealthTechDev",
    "RegTechExpert", "ComplianceTech", "AuditTechPro", "LegalTechDev", "GovTechExpert",
    "ClimateTechDev", "GreenTechPro", "SustainabilityTech", "EnergyTechExpert", "CleanTechDev",
    "SpaceTechEngineer", "AerospaceDev", "DefenseTechPro", "MilitaryTechExpert", "CyberWarfare",
    "QuantumComputing", "QuantumCrypto", "QuantumAlgorithm", "QuantumResearcher", "QuantumDev",
    "WebAssemblyPro", "EdgeComputingDev", "5GNetworkPro", "6GResearcher", "TelecomTechExpert",
    "ARVRDeveloper", "MetaverseDev", "XREngineer", "ImmersiveTechPro", "SpatialComputing",
    "Web3Developer", "DeFiEngineer", "NFTDeveloper", "DAOBuilder", "CryptoDeveloper",
    "SmartContractDev", "SolidityPro", "EthereumDev", "PolkadotDev", "SolanaDeveloper",
    "CrossChainDev", "Layer2Expert", "ZKProofDev", "ConsensusExpert", "TokenomicsDesigner",
    "TechInnovator", "DigitalTransformation", "TechStrategist", "InnovationManager", "TechVP",
    "ChiefArchitect", "PrincipalEngineer", "StaffEngineer", "SeniorEngineer", "LeadDeveloper",
    "TechDirector", "EngineeringManager", "VPEngineering", "CTO", "TechFounder",
    "SerialEntrepreneur", "TechInvestor", "VenturePartner", "AngelInvestor", "TechAdvisor",
    "IndustryExpert", "ThoughtLeader", "TechSpeaker", "ConferenceOrganizer", "CommunityBuilder",
    "OpenSourceMaintainer", "TechBlogger", "YouTubeTechCreator", "PodcastHost", "TechInfluencer",
    "CodeMentor", "TechCoach", "CareerAdvisor", "TalentAcquisition", "TechRecruiter",
    "RemoteWorker", "DigitalNomad", "FreelanceDev", "ContractorPro", "ConsultingExpert",
    "BootcampGrad", "SelfTaughtDev", "CareerChanger", "TechNewbie", "JuniorDeveloper",
    "MidLevelDev", "SeniorDev", "PrincipalDev", "DistinguishedEngineer", "TechFellow"
]

def get_random_english_nickname():
    """获取随机英文风格昵称"""
    return random.choice(ENGLISH_NICKNAMES)

def update_nickname_in_file(file_path):
    """更新单个文件中的昵称"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换昵称
        nickname_pattern = r'\*\*昵称\*\*：([^\n]*)'
        new_nickname = get_random_english_nickname()
        
        # 替换昵称
        updated_content = re.sub(nickname_pattern, f'**昵称**：{new_nickname}', content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True, new_nickname
        
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False, None

def main():
    """主函数"""
    print("开始批量修改昵称为英文风格...")
    
    # 获取所有面经文件
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    all_files = []
    if tech_dir.exists():
        all_files.extend(list(tech_dir.glob("*.md")))
    if non_tech_dir.exists():
        all_files.extend(list(non_tech_dir.glob("*.md")))
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    
    success_count = 0
    for file_path in all_files:
        success, new_nickname = update_nickname_in_file(file_path)
        if success:
            success_count += 1
            print(f"✅ {file_path.name} -> {new_nickname}")
    
    print(f"\n昵称修改完成！成功处理 {success_count}/{len(all_files)} 个文件")

if __name__ == "__main__":
    main()
