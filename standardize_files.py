#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面经文件标准化工具
统一格式、字数、排版等标准
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class InterviewFileStandardizer:
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.tech_dir = self.base_dir / "技术岗面经"
        self.non_tech_dir = self.base_dir / "非技术岗面经"
        
        # 标准化模板
        self.standard_template = """# {title}

## 👤 作者信息
{author_info}

## 📝 个人背景
{background}

## 🔍 面试流程
{interview_process}

## 📊 面试结果
{result}

## 💡 经验总结
{summary}
"""

    def extract_content_sections(self, content: str) -> Dict[str, str]:
        """提取文件内容的各个部分"""
        sections = {
            'title': '',
            'author_info': '',
            'background': '',
            'interview_process': '',
            'result': '',
            'summary': ''
        }
        
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            
            # 提取标题
            if line.startswith('# '):
                sections['title'] = line[2:].strip()
                continue
            
            # 识别章节
            if line.startswith('##') or line.startswith('###'):
                # 保存之前的章节内容
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                    current_content = []
                
                # 识别新章节
                header_text = re.sub(r'^#{2,}\s*', '', line).lower()
                if any(keyword in header_text for keyword in ['作者', '个人信息', '基本信息']):
                    current_section = 'author_info'
                elif any(keyword in header_text for keyword in ['背景', '简介', '个人经历']):
                    current_section = 'background'
                elif any(keyword in header_text for keyword in ['面试', '流程', '过程', '一面', '二面', '技术面']):
                    current_section = 'interview_process'
                elif any(keyword in header_text for keyword in ['结果', '录用', '通过', 'offer']):
                    current_section = 'result'
                elif any(keyword in header_text for keyword in ['总结', '经验', '建议', '感想', '反思']):
                    current_section = 'summary'
                else:
                    current_section = 'interview_process'  # 默认归类为面试流程
                
                # 如果是面试流程，保留原始标题
                if current_section == 'interview_process':
                    current_content.append(line)
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()
        
        return sections

    def format_author_info(self, raw_info: str) -> str:
        """标准化作者信息格式"""
        if not raw_info:
            return "- **作者**: [匿名]\n- **背景**: [待补充]\n- **面试时间**: [待补充]"
        
        # 提取关键信息
        info_lines = []
        for line in raw_info.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('**') or line.startswith('- '):
                    info_lines.append(line)
                elif line.startswith('作者') or line.startswith('姓名'):
                    info_lines.append(f"- **作者**: {line.split('：', 1)[-1].split(':', 1)[-1].strip()}")
                elif '学历' in line or '专业' in line:
                    info_lines.append(f"- **学历背景**: {line.split('：', 1)[-1].split(':', 1)[-1].strip()}")
                elif '经验' in line or '工作' in line:
                    info_lines.append(f"- **工作经验**: {line.split('：', 1)[-1].split(':', 1)[-1].strip()}")
                elif '时间' in line or '面试' in line:
                    info_lines.append(f"- **面试时间**: {line.split('：', 1)[-1].split(':', 1)[-1].strip()}")
        
        if not info_lines:
            return "- **作者**: [匿名]\n- **背景**: [待补充]\n- **面试时间**: [待补充]"
        
        return '\n'.join(info_lines[:4])  # 限制最多4行

    def format_background(self, raw_background: str) -> str:
        """标准化个人背景格式"""
        if not raw_background:
            return "[个人背景简介，100-200字]"
        
        # 移除多余的格式标记
        clean_bg = re.sub(r'^#{2,}\s*.*?\n', '', raw_background, flags=re.MULTILINE)
        clean_bg = re.sub(r'\*\*(.*?)\*\*', r'\1', clean_bg)
        clean_bg = re.sub(r'\n\s*\n', '\n', clean_bg).strip()
        
        # 控制长度
        if len(clean_bg) > 400:
            clean_bg = clean_bg[:350] + "..."
        
        return clean_bg if clean_bg else "[个人背景简介，100-200字]"

    def format_interview_process(self, raw_process: str) -> str:
        """标准化面试流程格式"""
        if not raw_process:
            return "### 一面：技术基础\n[面试内容]\n\n### 二面：综合面试\n[面试内容]"
        
        # 标准化面试轮次标题
        process = raw_process
        
        # 统一面试轮次格式
        process = re.sub(r'#{2,}\s*(第?[一二三四五六]?[轮次面]?[:：]?.*?)\n', r'### \1\n', process)
        process = re.sub(r'#{2,}\s*([一二三四五六七八九十]面[:：]?.*?)\n', r'### \1\n', process)
        process = re.sub(r'#{2,}\s*(.*?面试.*?)\n', r'### \1\n', process)
        
        # 清理多余的空行
        process = re.sub(r'\n\s*\n\s*\n', '\n\n', process).strip()
        
        # 限制代码块长度
        code_blocks = re.findall(r'```[\s\S]*?```', process)
        for block in code_blocks:
            if len(block.split('\n')) > 20:  # 限制代码块最多20行
                lines = block.split('\n')
                shortened = '\n'.join(lines[:20]) + '\n// ... 代码省略 ...\n```'
                process = process.replace(block, shortened)
        
        return process

    def format_result(self, raw_result: str) -> str:
        """标准化面试结果格式"""
        if not raw_result:
            return "[面试结果]"
        
        clean_result = re.sub(r'^#{2,}\s*.*?\n', '', raw_result, flags=re.MULTILINE)
        clean_result = re.sub(r'\n\s*\n', '\n', clean_result).strip()
        
        # 控制长度在200字以内
        if len(clean_result) > 200:
            clean_result = clean_result[:180] + "..."
        
        return clean_result if clean_result else "[面试结果]"

    def format_summary(self, raw_summary: str) -> str:
        """标准化经验总结格式"""
        if not raw_summary:
            return "[经验总结和建议，200-300字]"
        
        clean_summary = re.sub(r'^#{2,}\s*.*?\n', '', raw_summary, flags=re.MULTILINE)
        clean_summary = re.sub(r'\n\s*\n', '\n', clean_summary).strip()
        
        # 控制长度在500字以内
        if len(clean_summary) > 500:
            clean_summary = clean_summary[:450] + "..."
        
        return clean_summary if clean_summary else "[经验总结和建议，200-300字]"

    def standardize_file(self, file_path: Path) -> bool:
        """标准化单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取各部分内容
            sections = self.extract_content_sections(content)
            
            # 如果没有标题，从文件名提取
            if not sections['title']:
                sections['title'] = file_path.stem.split('. ', 1)[-1].replace('面经', '面经')
            
            # 格式化各部分
            formatted_content = self.standard_template.format(
                title=sections['title'],
                author_info=self.format_author_info(sections['author_info']),
                background=self.format_background(sections['background']),
                interview_process=self.format_interview_process(sections['interview_process']),
                result=self.format_result(sections['result']),
                summary=self.format_summary(sections['summary'])
            )
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_content)
            
            return True
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return False

    def standardize_directory(self, directory: Path) -> Tuple[int, int]:
        """标准化目录下的所有文件"""
        success_count = 0
        total_count = 0
        
        for file_path in directory.glob("*.md"):
            total_count += 1
            if self.standardize_file(file_path):
                success_count += 1
                print(f"✅ 已标准化: {file_path.name}")
            else:
                print(f"❌ 处理失败: {file_path.name}")
        
        return success_count, total_count

    def run_standardization(self):
        """运行完整的标准化流程"""
        print("🚀 开始标准化面经文件...")
        
        # 处理技术岗面经
        print("\n📁 处理技术岗面经...")
        tech_success, tech_total = self.standardize_directory(self.tech_dir)
        
        # 处理非技术岗面经
        print("\n📁 处理非技术岗面经...")
        non_tech_success, non_tech_total = self.standardize_directory(self.non_tech_dir)
        
        # 输出统计结果
        total_success = tech_success + non_tech_success
        total_files = tech_total + non_tech_total
        
        print(f"\n📊 标准化完成!")
        print(f"技术岗: {tech_success}/{tech_total} 成功")
        print(f"非技术岗: {non_tech_success}/{non_tech_total} 成功")
        print(f"总计: {total_success}/{total_files} 成功")
        print(f"成功率: {total_success/total_files*100:.1f}%")

def main():
    base_dir = "/Users/<USER>/Desktop/龙井面经"
    standardizer = InterviewFileStandardizer(base_dir)
    standardizer.run_standardization()

if __name__ == "__main__":
    main()