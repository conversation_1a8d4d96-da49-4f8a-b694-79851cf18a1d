#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将面试经验文档从Markdown转换为PDF格式
并按照指定的文件夹结构进行组织
"""

import os
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查必要的依赖是否安装"""
    try:
        # 检查pandoc是否安装
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 错误：pandoc未安装")
            print("请安装pandoc: brew install pandoc (macOS)")
            return False
        
        print("✅ pandoc已安装")
        return True
        
    except FileNotFoundError:
        print("❌ 错误：pandoc未找到")
        print("请安装pandoc: brew install pandoc (macOS)")
        return False

def create_output_directories():
    """创建输出目录结构"""
    base_dir = Path("面试经验文档PDF版本")
    tech_dir = base_dir / "技术岗面经PDF"
    non_tech_dir = base_dir / "非技术岗面经PDF"
    
    # 创建目录
    tech_dir.mkdir(parents=True, exist_ok=True)
    non_tech_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 创建目录结构：")
    print(f"   📁 {base_dir}")
    print(f"   ├── 📁 {tech_dir.name}")
    print(f"   └── 📁 {non_tech_dir.name}")
    
    return base_dir, tech_dir, non_tech_dir

def convert_md_to_pdf(md_file, output_dir):
    """将单个Markdown文件转换为PDF"""
    try:
        # 生成PDF文件名
        pdf_name = md_file.stem + ".pdf"
        pdf_path = output_dir / pdf_name

        # 首先尝试使用wkhtmltopdf（如果可用）
        try:
            # 先转换为HTML，再转换为PDF
            html_path = output_dir / (md_file.stem + ".html")

            # Markdown to HTML
            cmd_html = [
                'pandoc',
                str(md_file),
                '-o', str(html_path),
                '--standalone',
                '--highlight-style=github',
                '--css', 'https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css',
                '--metadata', 'title=' + md_file.stem,
            ]

            result_html = subprocess.run(cmd_html, capture_output=True, text=True)

            if result_html.returncode == 0:
                # HTML to PDF using wkhtmltopdf
                cmd_pdf = [
                    'wkhtmltopdf',
                    '--page-size', 'A4',
                    '--margin-top', '20mm',
                    '--margin-bottom', '20mm',
                    '--margin-left', '20mm',
                    '--margin-right', '20mm',
                    '--encoding', 'UTF-8',
                    '--enable-local-file-access',
                    str(html_path),
                    str(pdf_path)
                ]

                result_pdf = subprocess.run(cmd_pdf, capture_output=True, text=True)

                # 清理临时HTML文件
                if html_path.exists():
                    html_path.unlink()

                if result_pdf.returncode == 0:
                    return True, pdf_name

        except FileNotFoundError:
            # wkhtmltopdf不可用，使用pandoc默认引擎
            pass

        # 使用pandoc默认PDF引擎
        cmd = [
            'pandoc',
            str(md_file),
            '-o', str(pdf_path),
            '--highlight-style=github',
            '--toc',
            '--toc-depth=3',
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            return True, pdf_name
        else:
            print(f"❌ 转换失败: {md_file.name}")
            print(f"   错误信息: {result.stderr}")
            return False, None

    except Exception as e:
        print(f"❌ 转换异常: {md_file.name} - {e}")
        return False, None

def get_all_markdown_files():
    """获取所有Markdown文件"""
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    tech_files = []
    non_tech_files = []
    
    if tech_dir.exists():
        tech_files = list(tech_dir.glob("*.md"))
        tech_files.sort()
    
    if non_tech_dir.exists():
        non_tech_files = list(non_tech_dir.glob("*.md"))
        non_tech_files.sort()
    
    return tech_files, non_tech_files

def main():
    """主函数"""
    print("🚀 开始将面试经验文档转换为PDF格式...")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建输出目录
    base_dir, tech_pdf_dir, non_tech_pdf_dir = create_output_directories()
    
    # 获取所有Markdown文件
    tech_files, non_tech_files = get_all_markdown_files()
    
    print(f"\n📊 文件统计：")
    print(f"   技术岗面经: {len(tech_files)} 个文件")
    print(f"   非技术岗面经: {len(non_tech_files)} 个文件")
    print(f"   总计: {len(tech_files) + len(non_tech_files)} 个文件")
    
    # 转换统计
    tech_success = 0
    tech_failed = 0
    non_tech_success = 0
    non_tech_failed = 0
    
    print(f"\n🔄 开始转换技术岗面经...")
    for i, md_file in enumerate(tech_files, 1):
        print(f"   [{i:3d}/{len(tech_files)}] 转换中: {md_file.name}")
        success, pdf_name = convert_md_to_pdf(md_file, tech_pdf_dir)
        if success:
            tech_success += 1
            print(f"   ✅ 成功: {pdf_name}")
        else:
            tech_failed += 1
    
    print(f"\n🔄 开始转换非技术岗面经...")
    for i, md_file in enumerate(non_tech_files, 1):
        print(f"   [{i:3d}/{len(non_tech_files)}] 转换中: {md_file.name}")
        success, pdf_name = convert_md_to_pdf(md_file, non_tech_pdf_dir)
        if success:
            non_tech_success += 1
            print(f"   ✅ 成功: {pdf_name}")
        else:
            non_tech_failed += 1
    
    # 输出结果统计
    print(f"\n" + "=" * 60)
    print(f"📈 转换完成统计：")
    print(f"   技术岗面经: {tech_success}/{len(tech_files)} 成功 ({tech_success/len(tech_files)*100:.1f}%)")
    print(f"   非技术岗面经: {non_tech_success}/{len(non_tech_files)} 成功 ({non_tech_success/len(non_tech_files)*100:.1f}%)")
    print(f"   总体成功率: {(tech_success + non_tech_success)/(len(tech_files) + len(non_tech_files))*100:.1f}%")
    
    if tech_failed > 0 or non_tech_failed > 0:
        print(f"\n⚠️  失败文件数: {tech_failed + non_tech_failed}")
    
    print(f"\n📁 输出目录结构：")
    print(f"   {base_dir}/")
    print(f"   ├── {tech_pdf_dir.name}/ ({tech_success} 个PDF文件)")
    print(f"   └── {non_tech_pdf_dir.name}/ ({non_tech_success} 个PDF文件)")
    
    print(f"\n🎉 PDF转换任务完成！")

if __name__ == "__main__":
    main()
