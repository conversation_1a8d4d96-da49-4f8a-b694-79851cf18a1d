<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>43. 百度-自动驾驶-感知算法工程师面经</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
    /* CSS for syntax highlighting */
    html { -webkit-text-size-adjust: 100%; }
    pre > code.sourceCode { white-space: pre; position: relative; }
    pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
    pre > code.sourceCode > span:empty { height: 1.2em; }
    .sourceCode { overflow: visible; }
    code.sourceCode > span { color: inherit; text-decoration: inherit; }
    div.sourceCode { margin: 1em 0; }
    pre.sourceCode { margin: 0; }
    @media screen {
    div.sourceCode { overflow: auto; }
    }
    @media print {
    pre > code.sourceCode { white-space: pre-wrap; }
    pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
    }
    pre.numberSource code
      { counter-reset: source-line 0; }
    pre.numberSource code > span
      { position: relative; left: -4em; counter-increment: source-line; }
    pre.numberSource code > span > a:first-child::before
      { content: counter(source-line);
        position: relative; left: -1em; text-align: right; vertical-align: baseline;
        border: none; display: inline-block;
        -webkit-touch-callout: none; -webkit-user-select: none;
        -khtml-user-select: none; -moz-user-select: none;
        -ms-user-select: none; user-select: none;
        padding: 0 4px; width: 4em;
        color: #aaaaaa;
      }
    pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
    div.sourceCode
      {   }
    @media screen {
    pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
    }
    code span.al { color: #ff0000; font-weight: bold; } /* Alert */
    code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
    code span.at { color: #7d9029; } /* Attribute */
    code span.bn { color: #40a070; } /* BaseN */
    code span.bu { color: #008000; } /* BuiltIn */
    code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
    code span.ch { color: #4070a0; } /* Char */
    code span.cn { color: #880000; } /* Constant */
    code span.co { color: #60a0b0; font-style: italic; } /* Comment */
    code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
    code span.do { color: #ba2121; font-style: italic; } /* Documentation */
    code span.dt { color: #902000; } /* DataType */
    code span.dv { color: #40a070; } /* DecVal */
    code span.er { color: #ff0000; font-weight: bold; } /* Error */
    code span.ex { } /* Extension */
    code span.fl { color: #40a070; } /* Float */
    code span.fu { color: #06287e; } /* Function */
    code span.im { color: #008000; font-weight: bold; } /* Import */
    code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
    code span.kw { color: #007020; font-weight: bold; } /* Keyword */
    code span.op { color: #666666; } /* Operator */
    code span.ot { color: #007020; } /* Other */
    code span.pp { color: #bc7a00; } /* Preprocessor */
    code span.sc { color: #4070a0; } /* SpecialChar */
    code span.ss { color: #bb6688; } /* SpecialString */
    code span.st { color: #4070a0; } /* String */
    code span.va { color: #19177c; } /* Variable */
    code span.vs { color: #4070a0; } /* VerbatimString */
    code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <link rel="stylesheet" href="data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9IHByZSBjb2RlIHsgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IHBhZGRpbmc6IDA7IH0gYmxvY2txdW90ZSB7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDE2cHg7IGNvbG9yOiAjNmE3MzdkOyB9IHRhYmxlIHsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgd2lkdGg6IDEwMCU7IG1hcmdpbjogMTZweCAwOyB9IHRoLCB0ZCB7IGJvcmRlcjogMXB4IHNvbGlkICNkZmUyZTU7IHBhZGRpbmc6IDhweCAxMnB4OyB0ZXh0LWFsaWduOiBsZWZ0OyB9IHRoIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsgZm9udC13ZWlnaHQ6IDYwMDsgfSB1bCwgb2wgeyBwYWRkaW5nLWxlZnQ6IDI0cHg7IH0gbGkgeyBtYXJnaW46IDRweCAwOyB9" />
</head>
<body>
<header id="title-block-header">
<h1 class="title">43. 百度-自动驾驶-感知算法工程师面经</h1>
</header>
<h1
id="百度-自动驾驶-感知算法工程师面经">百度-自动驾驶-感知算法工程师面经</h1>
<h2 id="作者信息">作者信息</h2>
<p><strong>昵称</strong>：草莓代码
<strong>背景</strong>：硕士毕业，计算机视觉专业，有4年感知算法开发经验。熟悉深度学习、计算机视觉和自动驾驶感知技术，对Apollo平台有深入了解。
<strong>面试结果</strong>：通过</p>
<h2 id="个人背景介绍">个人背景介绍</h2>
<p>我硕士学的计算机视觉，研究方向是目标检测和语义分割。毕业后在一家自动驾驶公司做感知算法工程师，主要负责视觉感知算法的开发和优化。</p>
<p>我们公司的自动驾驶系统已经在多个城市进行路测，我主要负责： 1.
目标检测算法开发，使用YOLO、SSD等网络架构 2.
语义分割算法优化，实现道路、车辆、行人的精确分割 3.
多传感器融合，结合摄像头、激光雷达、毫米波雷达数据 4.
算法工程化部署，在车载计算平台上实现实时推理</p>
<p>最有成就感的项目是优化了车辆检测算法，通过数据增强、网络结构改进和后处理优化，将检测精度从92%提升到96%，同时推理速度提升了30%。</p>
<p>选择百度主要是被Apollo平台的技术实力吸引。百度在自动驾驶领域起步较早，技术积累深厚，特别是在感知、定位、规划等核心技术方面都有领先优势。我希望能在这样的平台上，参与更大规模的自动驾驶系统开发。</p>
<h2 id="面试准备过程">面试准备过程</h2>
<p>收到面试通知后，我花了4周时间做深入准备：</p>
<p><strong>计算机视觉技术深化</strong>： -
深入学习了最新的目标检测算法，如YOLOX、DETR、Swin Transformer等 -
研究了语义分割的前沿技术，如SegFormer、Mask2Former等 -
学习了3D目标检测和BEV感知的相关技术</p>
<p><strong>自动驾驶感知技术</strong>： -
深入了解了Apollo感知模块的技术架构和算法实现 -
研究了多传感器融合的技术方案和工程实践 -
学习了端到端感知算法的最新进展</p>
<p><strong>深度学习框架</strong>： -
深入学习了PaddlePaddle框架，这是百度主推的深度学习框架 -
研究了模型压缩、量化、蒸馏等模型优化技术 -
学习了TensorRT、OpenVINO等推理加速框架</p>
<p><strong>项目案例准备</strong>： -
准备了5个核心感知算法项目的详细技术方案 -
整理了算法优化、工程部署、性能调优的具体案例 -
总结了多传感器融合和算法工程化的经验</p>
<h2 id="面试流程详细描述">面试流程详细描述</h2>
<h3 id="一面计算机视觉基础面试---75分钟">一面（计算机视觉基础面试 -
75分钟）</h3>
<p>面试官是百度自动驾驶感知团队的资深算法工程师，主要考察计算机视觉和深度学习基础。面试在百度科技园进行，环境很现代化。</p>
<p><strong>深度学习基础考察</strong>
面试官先问了一些深度学习的核心问题：</p>
<p>“说说卷积神经网络的基本原理？”
我从卷积操作、池化、激活函数开始，详细解释了CNN的前向传播和反向传播过程，还提到了不同卷积类型（标准卷积、深度可分离卷积、空洞卷积）的特点。</p>
<p>“Batch Normalization的作用和原理？”
我解释了BN解决内部协变量偏移的问题，加速训练收敛，还对比了Layer
Norm、Group Norm等其他归一化方法的差异。</p>
<p>“目标检测算法的发展历程？” 我从两阶段检测器（R-CNN、Fast
R-CNN、Faster
R-CNN）讲到单阶段检测器（YOLO、SSD），再到最新的Transformer-based方法（DETR、Deformable
DETR），分析了各自的优缺点。</p>
<p><strong>计算机视觉专业问题</strong>
“如何处理目标检测中的多尺度问题？” 我回答：“主要有几种方法：1.
特征金字塔网络（FPN）；2. 多尺度训练和测试；3. 空洞卷积增大感受野；4.
不同尺度的anchor设计。在实际项目中，我们使用了FPN+多尺度训练的组合方案。”</p>
<p>“语义分割和实例分割的区别？”
我详细对比了两者的定义、应用场景和技术实现，还提到了全景分割这个更复杂的任务。</p>
<p><strong>自动驾驶感知经验</strong> 我分享了车辆检测项目的技术细节：
“我们的车辆检测系统需要在各种天气和光照条件下稳定工作。我采用了YOLOv5作为基础架构，针对车载场景做了几个关键优化：</p>
<ol type="1">
<li>数据增强：使用Mosaic、MixUp、CutMix等技术增强模型泛化能力</li>
<li>网络改进：引入注意力机制（CBAM）提升特征表达能力</li>
<li>损失函数优化：使用Focal Loss解决正负样本不平衡问题</li>
<li>后处理优化：改进NMS算法，减少漏检和误检”</li>
</ol>
<p><strong>编程题</strong>
题目是”实现非极大值抑制（NMS）算法”，我用Python实现了完整版本：</p>
<div class="sourceCode" id="cb1"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> numpy <span class="im">as</span> np</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> nms(boxes, scores, iou_threshold):</span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>    <span class="co">&quot;&quot;&quot;</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="co">    非极大值抑制算法</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a><span class="co">    Args:</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a><span class="co">        boxes: 边界框坐标 (N, 4) [x1, y1, x2, y2]</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a><span class="co">        scores: 置信度分数 (N,)</span></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a><span class="co">        iou_threshold: IoU阈值</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a><span class="co">    Returns:</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a><span class="co">        keep: 保留的边界框索引</span></span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a><span class="co">    &quot;&quot;&quot;</span></span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 计算边界框面积</span></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>    areas <span class="op">=</span> (boxes[:, <span class="dv">2</span>] <span class="op">-</span> boxes[:, <span class="dv">0</span>]) <span class="op">*</span> (boxes[:, <span class="dv">3</span>] <span class="op">-</span> boxes[:, <span class="dv">1</span>])</span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 按置信度降序排序</span></span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a>    order <span class="op">=</span> scores.argsort()[::<span class="op">-</span><span class="dv">1</span>]</span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a>    keep <span class="op">=</span> []</span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a>    <span class="cf">while</span> order.size <span class="op">&gt;</span> <span class="dv">0</span>:</span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>        <span class="co"># 保留置信度最高的框</span></span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>        i <span class="op">=</span> order[<span class="dv">0</span>]</span>
<span id="cb1-23"><a href="#cb1-23" aria-hidden="true" tabindex="-1"></a>        keep.append(i)</span>
<span id="cb1-24"><a href="#cb1-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-25"><a href="#cb1-25" aria-hidden="true" tabindex="-1"></a>        <span class="co"># 计算IoU</span></span>
<span id="cb1-26"><a href="#cb1-26" aria-hidden="true" tabindex="-1"></a>        xx1 <span class="op">=</span> np.maximum(boxes[i, <span class="dv">0</span>], boxes[order[<span class="dv">1</span>:], <span class="dv">0</span>])</span>
<span id="cb1-27"><a href="#cb1-27" aria-hidden="true" tabindex="-1"></a>        yy1 <span class="op">=</span> np.maximum(boxes[i, <span class="dv">1</span>], boxes[order[<span class="dv">1</span>:], <span class="dv">1</span>])</span>
<span id="cb1-28"><a href="#cb1-28" aria-hidden="true" tabindex="-1"></a>        xx2 <span class="op">=</span> np.minimum(boxes[i, <span class="dv">2</span>], boxes[order[<span class="dv">1</span>:], <span class="dv">2</span>])</span>
<span id="cb1-29"><a href="#cb1-29" aria-hidden="true" tabindex="-1"></a>        yy2 <span class="op">=</span> np.minimum(boxes[i, <span class="dv">3</span>], boxes[order[<span class="dv">1</span>:], <span class="dv">3</span>])</span>
<span id="cb1-30"><a href="#cb1-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-31"><a href="#cb1-31" aria-hidden="true" tabindex="-1"></a>        w <span class="op">=</span> np.maximum(<span class="dv">0</span>, xx2 <span class="op">-</span> xx1)</span>
<span id="cb1-32"><a href="#cb1-32" aria-hidden="true" tabindex="-1"></a>        h <span class="op">=</span> np.maximum(<span class="dv">0</span>, yy2 <span class="op">-</span> yy1)</span>
<span id="cb1-33"><a href="#cb1-33" aria-hidden="true" tabindex="-1"></a>        intersection <span class="op">=</span> w <span class="op">*</span> h</span>
<span id="cb1-34"><a href="#cb1-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-35"><a href="#cb1-35" aria-hidden="true" tabindex="-1"></a>        iou <span class="op">=</span> intersection <span class="op">/</span> (areas[i] <span class="op">+</span> areas[order[<span class="dv">1</span>:]] <span class="op">-</span> intersection)</span>
<span id="cb1-36"><a href="#cb1-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-37"><a href="#cb1-37" aria-hidden="true" tabindex="-1"></a>        <span class="co"># 保留IoU小于阈值的框</span></span>
<span id="cb1-38"><a href="#cb1-38" aria-hidden="true" tabindex="-1"></a>        inds <span class="op">=</span> np.where(iou <span class="op">&lt;=</span> iou_threshold)[<span class="dv">0</span>]</span>
<span id="cb1-39"><a href="#cb1-39" aria-hidden="true" tabindex="-1"></a>        order <span class="op">=</span> order[inds <span class="op">+</span> <span class="dv">1</span>]</span>
<span id="cb1-40"><a href="#cb1-40" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-41"><a href="#cb1-41" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> keep</span></code></pre></div>
<p>面试官对我的实现很满意，还问了Soft-NMS和DIoU-NMS等改进算法的原理。</p>
<h3 id="二面技术深度面试---75分钟">二面（技术深度面试 - 75分钟）</h3>
<p>面试官是自动驾驶的技术专家，重点考察技术深度和系统设计能力。</p>
<p><strong>系统设计</strong> - 设计一个高并发的感知算法工程师系统 -
考虑可扩展性、可用性、一致性等技术指标 -
数据库设计、缓存策略、消息队列等技术选型 -
监控告警、降级熔断等保障措施</p>
<p><strong>技术深度挖掘</strong> - 深入讨论使用过的技术栈的底层原理 -
性能优化的具体实践和效果 - 技术难点的解决思路和方案对比 -
技术趋势的理解和前瞻性思考</p>
<p>我详细设计了一个完整的系统架构，从前端到后端、从存储到网络的全链路技术方案，面试官对设计思路比较满意。</p>
<h3 id="三面综合能力面试---60分钟">三面（综合能力面试 - 60分钟）</h3>
<p>面试官是部门负责人，考察综合素质和发展潜力。</p>
<p><strong>技术视野</strong> - 对行业技术发展趋势的看法 -
新技术的学习方法和实践经验 - 技术选型的决策思路和评估标准 -
团队技术管理和代码质量保证</p>
<p><strong>项目管理</strong> - 项目开发流程和质量保证 -
团队协作和沟通协调能力 - 技术债务管理和重构经验 -
跨部门合作的实践经验</p>
<p><strong>学习成长</strong> - 技术学习路径和成长规划 -
遇到技术瓶颈时的突破方法 - 在百度的职业发展期望 -
对自动驾驶业务的理解和想法</p>
<p>面试官比较认可我的技术深度和学习能力，特别是对感知算法工程师领域的理解和规划。</p>
<h3 id="四面hr面试---30分钟">四面（HR面试 - 30分钟）</h3>
<p>HR面试主要了解个人情况和求职动机。</p>
<ul>
<li>为什么选择百度和自动驾驶</li>
<li>职业规划和发展期望</li>
<li>薪资期望和福利关注点</li>
<li>工作节奏和压力承受能力</li>
<li>入职时间和其他面试进展</li>
</ul>
<p>整体沟通很顺畅，HR对我的背景和动机比较认可。</p>
<h2 id="面试结果">面试结果</h2>
<p>面试过程很专业，最终成功拿到offer。对未来的工作很期待。</p>
<h2 id="经验总结">经验总结</h2>
<p><strong>技术准备建议：</strong> 1.
<strong>基础要扎实</strong>：技术能力，项目经验，学习能力，团队合作等核心能力必须过关
2.
<strong>项目要深入</strong>：准备2-3个有深度的项目案例，能讲清楚技术方案和思考过程
3. <strong>视野要开阔</strong>：关注技术发展趋势，有自己的思考和见解 4.
<strong>表达要清晰</strong>：技术方案要能清楚表达，逻辑性要强</p>
<p><strong>面试技巧方面：</strong> 1.
<strong>准备要充分</strong>：提前了解公司技术栈和业务特点 2.
<strong>回答要结构化</strong>：先总结后展开，逻辑清晰 3.
<strong>态度要诚恳</strong>：不会的坦诚说明，展现学习意愿 4.
<strong>互动要积极</strong>：主动提问，展现对公司和岗位的兴趣</p>
<p>百度的面试整体很专业，注重技术实力的同时也看重学习能力和发展潜力。建议准备面试的同学重点关注技术深度和项目经验，同时要展现出持续学习的能力和对技术的热情。</p>
<p>希望这个面经能对准备感知算法工程师岗位面试的同学有所帮助！</p>
</body>
</html>
