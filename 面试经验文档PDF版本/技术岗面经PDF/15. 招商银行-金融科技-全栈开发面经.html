<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>15. 招商银行-金融科技-全栈开发面经</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
  </style>
  <link rel="stylesheet" href="data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9IHByZSBjb2RlIHsgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IHBhZGRpbmc6IDA7IH0gYmxvY2txdW90ZSB7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDE2cHg7IGNvbG9yOiAjNmE3MzdkOyB9IHRhYmxlIHsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgd2lkdGg6IDEwMCU7IG1hcmdpbjogMTZweCAwOyB9IHRoLCB0ZCB7IGJvcmRlcjogMXB4IHNvbGlkICNkZmUyZTU7IHBhZGRpbmc6IDhweCAxMnB4OyB0ZXh0LWFsaWduOiBsZWZ0OyB9IHRoIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsgZm9udC13ZWlnaHQ6IDYwMDsgfSB1bCwgb2wgeyBwYWRkaW5nLWxlZnQ6IDI0cHg7IH0gbGkgeyBtYXJnaW46IDRweCAwOyB9" />
</head>
<body>
<header id="title-block-header">
<h1 class="title">15. 招商银行-金融科技-全栈开发面经</h1>
</header>
<h1
id="招商银行-金融科技-全栈开发面经">招商银行-金融科技-全栈开发面经</h1>
<h2 id="作者信息">作者信息</h2>
<p><strong>昵称</strong>：逃跑计划B
<strong>背景</strong>：计算机相关专业毕业，有全栈开发开发经验。有丰富的项目经验和扎实的技术基础，对行业发展有深入理解。<br />
<strong>面试结果</strong>：通过</p>
<h2 id="个人背景">个人背景</h2>
<p>计算机相关专业毕业，有全栈开发开发经验。有丰富的项目经验和扎实的技术基础，对行业发展有深入理解。</p>
<p>选择招商银行是因为其在技术领域的领先地位和良好的工程师文化。金融科技作为核心技术部门，在全栈开发方向有很多技术挑战和成长机会，正是我希望深入发展的方向。</p>
<h2 id="面试流程">面试流程</h2>
<h3 id="一面技术基础面试---60分钟">一面（技术基础面试 - 60分钟）</h3>
<p>面试官是金融科技的高级工程师，主要考察技术基础能力。</p>
<p><strong>技术基础考察</strong> -
编程语言基础：数据结构、算法复杂度分析 -
计算机基础：操作系统、网络、数据库原理 -
开发框架：主流技术栈的使用和原理理解 -
项目经验：详细介绍参与过的技术项目</p>
<p>我重点介绍了之前负责的核心项目，从技术选型、架构设计到性能优化的完整过程，面试官对技术深度比较认可。</p>
<p><strong>算法编程题</strong> -
中等难度的算法题，考察编程思维和代码质量 -
要求分析时间复杂度和空间复杂度 - 需要考虑边界条件和异常处理</p>
<p>整体感觉一面主要是技术基础的全面考察，面试官很专业，会根据回答情况深入追问。</p>
<h3 id="二面技术深度面试---75分钟">二面（技术深度面试 - 75分钟）</h3>
<p>面试官是金融科技的技术专家，重点考察技术深度和系统设计能力。</p>
<p><strong>系统设计</strong> - 设计一个高并发的全栈开发系统 -
考虑可扩展性、可用性、一致性等技术指标 -
数据库设计、缓存策略、消息队列等技术选型 -
监控告警、降级熔断等保障措施</p>
<p><strong>技术深度挖掘</strong> - 深入讨论使用过的技术栈的底层原理 -
性能优化的具体实践和效果 - 技术难点的解决思路和方案对比 -
技术趋势的理解和前瞻性思考</p>
<p>我详细设计了一个完整的系统架构，从前端到后端、从存储到网络的全链路技术方案，面试官对设计思路比较满意。</p>
<h3 id="三面综合能力面试---60分钟">三面（综合能力面试 - 60分钟）</h3>
<p>面试官是部门负责人，考察综合素质和发展潜力。</p>
<p><strong>技术视野</strong> - 对行业技术发展趋势的看法 -
新技术的学习方法和实践经验 - 技术选型的决策思路和评估标准 -
团队技术管理和代码质量保证</p>
<p><strong>项目管理</strong> - 项目开发流程和质量保证 -
团队协作和沟通协调能力 - 技术债务管理和重构经验 -
跨部门合作的实践经验</p>
<p><strong>学习成长</strong> - 技术学习路径和成长规划 -
遇到技术瓶颈时的突破方法 - 在招商银行的职业发展期望 -
对金融科技业务的理解和想法</p>
<p>面试官比较认可我的技术深度和学习能力，特别是对全栈开发领域的理解和规划。</p>
<h3 id="四面hr面试---30分钟">四面（HR面试 - 30分钟）</h3>
<p>HR面试主要了解个人情况和求职动机。</p>
<ul>
<li>为什么选择招商银行和金融科技</li>
<li>职业规划和发展期望</li>
<li>薪资期望和福利关注点</li>
<li>工作节奏和压力承受能力</li>
<li>入职时间和其他面试进展</li>
</ul>
<p>整体沟通很顺畅，HR对我的背景和动机比较认可。</p>
<h2 id="面试结果">面试结果</h2>
<p>面试过程很专业，最终成功拿到offer。对未来的工作很期待。</p>
<h2 id="经验总结">经验总结</h2>
<p><strong>技术准备建议：</strong> 1.
<strong>基础要扎实</strong>：技术能力，项目经验，学习能力，团队合作等核心能力必须过关
2.
<strong>项目要深入</strong>：准备2-3个有深度的项目案例，能讲清楚技术方案和思考过程
3. <strong>视野要开阔</strong>：关注技术发展趋势，有自己的思考和见解 4.
<strong>表达要清晰</strong>：技术方案要能清楚表达，逻辑性要强</p>
<p><strong>面试技巧方面：</strong> 1.
<strong>准备要充分</strong>：提前了解公司技术栈和业务特点 2.
<strong>回答要结构化</strong>：先总结后展开，逻辑清晰 3.
<strong>态度要诚恳</strong>：不会的坦诚说明，展现学习意愿 4.
<strong>互动要积极</strong>：主动提问，展现对公司和岗位的兴趣</p>
<p>招商银行的面试整体很专业，注重技术实力的同时也看重学习能力和发展潜力。建议准备面试的同学重点关注技术深度和项目经验，同时要展现出持续学习的能力和对技术的热情。</p>
<p>希望这个面经能对准备全栈开发岗位面试的同学有所帮助！</p>
</body>
</html>
