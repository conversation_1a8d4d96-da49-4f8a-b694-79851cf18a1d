<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>01. 腾讯-微信事业群-前端开发面经</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
    /* CSS for syntax highlighting */
    html { -webkit-text-size-adjust: 100%; }
    pre > code.sourceCode { white-space: pre; position: relative; }
    pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
    pre > code.sourceCode > span:empty { height: 1.2em; }
    .sourceCode { overflow: visible; }
    code.sourceCode > span { color: inherit; text-decoration: inherit; }
    div.sourceCode { margin: 1em 0; }
    pre.sourceCode { margin: 0; }
    @media screen {
    div.sourceCode { overflow: auto; }
    }
    @media print {
    pre > code.sourceCode { white-space: pre-wrap; }
    pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
    }
    pre.numberSource code
      { counter-reset: source-line 0; }
    pre.numberSource code > span
      { position: relative; left: -4em; counter-increment: source-line; }
    pre.numberSource code > span > a:first-child::before
      { content: counter(source-line);
        position: relative; left: -1em; text-align: right; vertical-align: baseline;
        border: none; display: inline-block;
        -webkit-touch-callout: none; -webkit-user-select: none;
        -khtml-user-select: none; -moz-user-select: none;
        -ms-user-select: none; user-select: none;
        padding: 0 4px; width: 4em;
        color: #aaaaaa;
      }
    pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
    div.sourceCode
      {   }
    @media screen {
    pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
    }
    code span.al { color: #ff0000; font-weight: bold; } /* Alert */
    code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
    code span.at { color: #7d9029; } /* Attribute */
    code span.bn { color: #40a070; } /* BaseN */
    code span.bu { color: #008000; } /* BuiltIn */
    code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
    code span.ch { color: #4070a0; } /* Char */
    code span.cn { color: #880000; } /* Constant */
    code span.co { color: #60a0b0; font-style: italic; } /* Comment */
    code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
    code span.do { color: #ba2121; font-style: italic; } /* Documentation */
    code span.dt { color: #902000; } /* DataType */
    code span.dv { color: #40a070; } /* DecVal */
    code span.er { color: #ff0000; font-weight: bold; } /* Error */
    code span.ex { } /* Extension */
    code span.fl { color: #40a070; } /* Float */
    code span.fu { color: #06287e; } /* Function */
    code span.im { color: #008000; font-weight: bold; } /* Import */
    code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
    code span.kw { color: #007020; font-weight: bold; } /* Keyword */
    code span.op { color: #666666; } /* Operator */
    code span.ot { color: #007020; } /* Other */
    code span.pp { color: #bc7a00; } /* Preprocessor */
    code span.sc { color: #4070a0; } /* SpecialChar */
    code span.ss { color: #bb6688; } /* SpecialString */
    code span.st { color: #4070a0; } /* String */
    code span.va { color: #19177c; } /* Variable */
    code span.vs { color: #4070a0; } /* VerbatimString */
    code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <link rel="stylesheet" href="data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9IHByZSBjb2RlIHsgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IHBhZGRpbmc6IDA7IH0gYmxvY2txdW90ZSB7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDE2cHg7IGNvbG9yOiAjNmE3MzdkOyB9IHRhYmxlIHsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgd2lkdGg6IDEwMCU7IG1hcmdpbjogMTZweCAwOyB9IHRoLCB0ZCB7IGJvcmRlcjogMXB4IHNvbGlkICNkZmUyZTU7IHBhZGRpbmc6IDhweCAxMnB4OyB0ZXh0LWFsaWduOiBsZWZ0OyB9IHRoIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsgZm9udC13ZWlnaHQ6IDYwMDsgfSB1bCwgb2wgeyBwYWRkaW5nLWxlZnQ6IDI0cHg7IH0gbGkgeyBtYXJnaW46IDRweCAwOyB9" />
</head>
<body>
<header id="title-block-header">
<h1 class="title">01. 腾讯-微信事业群-前端开发面经</h1>
</header>
<h1 id="腾讯-微信事业群-前端开发面经">腾讯-微信事业群-前端开发面经</h1>
<h2 id="作者信息">作者信息</h2>
<p><strong>昵称</strong>：月亮邮差
<strong>背景</strong>：本科毕业，计算机科学与技术专业，有3年前端开发经验。主要技术栈是Vue、React、Node.js，参与过几个中大型项目的开发。
<strong>面试结果</strong>：通过</p>
<h2 id="个人背景介绍">个人背景介绍</h2>
<p>我本科学的计算机，毕业后一直在做前端开发。第一份工作在一家创业公司，主要用Vue做后台管理系统，虽然项目不大但学到了很多基础知识。后来跳到一家中型互联网公司，开始接触React和微服务架构，参与了几个用户量比较大的项目。</p>
<p>技术方面，我比较熟悉Vue全家桶和React生态，Node.js也有一些经验。最近在学习TypeScript和微前端相关的技术。平时喜欢看技术博客，也会在GitHub上贡献一些开源项目。</p>
<p>选择腾讯主要是因为微信的技术影响力，我觉得能在这样的平台上工作是很好的学习机会。而且听说腾讯的技术氛围很好，工程师文化比较浓厚。</p>
<h2 id="面试准备过程">面试准备过程</h2>
<p>收到面试通知后，我花了大概3周时间准备：</p>
<p><strong>技术基础复习</strong>：重新梳理了JavaScript基础、浏览器原理、网络协议等知识点，特别是一些容易被忽略的细节。</p>
<p><strong>算法刷题</strong>：在LeetCode上刷了大概100道题，主要是中等难度的，重点练习了数组、链表、树、动态规划等常考类型。</p>
<p><strong>项目总结</strong>：把之前做过的项目重新梳理了一遍，整理了技术方案、遇到的问题、解决思路等，准备了几个比较有代表性的案例。</p>
<p><strong>技术深度学习</strong>：深入学习了一些前端进阶知识，比如Webpack原理、Vue源码、性能优化等。</p>
<h2 id="面试流程详细描述">面试流程详细描述</h2>
<h3 id="一面技术基础面试---60分钟">一面（技术基础面试 - 60分钟）</h3>
<p>面试官是微信事业群的高级前端工程师，看起来很年轻，技术功底很扎实。面试在腾讯滨海大厦进行，环境很好。</p>
<p><strong>JavaScript基础考察</strong>
面试官先问了一些JavaScript基础问题：</p>
<p>“说说var、let、const的区别？”
我回答了作用域、变量提升、重复声明等方面的差异，面试官又追问：“那你知道暂时性死区吗？”我解释了let和const在声明前不能访问的特性。</p>
<p>“手写一个深拷贝函数？”
我写了一个递归版本的深拷贝，考虑了对象、数组、null、循环引用等情况：</p>
<div class="sourceCode" id="cb1"><pre
class="sourceCode javascript"><code class="sourceCode javascript"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="kw">function</span> <span class="fu">deepClone</span>(obj<span class="op">,</span> map <span class="op">=</span> <span class="kw">new</span> <span class="bu">WeakMap</span>()) {</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a>  <span class="cf">if</span> (obj <span class="op">===</span> <span class="kw">null</span> <span class="op">||</span> <span class="kw">typeof</span> obj <span class="op">!==</span> <span class="st">&#39;object&#39;</span>) <span class="cf">return</span> obj<span class="op">;</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a>  <span class="cf">if</span> (map<span class="op">.</span><span class="fu">has</span>(obj)) <span class="cf">return</span> map<span class="op">.</span><span class="fu">get</span>(obj)<span class="op">;</span></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a>  <span class="kw">let</span> cloned <span class="op">=</span> <span class="bu">Array</span><span class="op">.</span><span class="fu">isArray</span>(obj) <span class="op">?</span> [] <span class="op">:</span> {}<span class="op">;</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a>  map<span class="op">.</span><span class="fu">set</span>(obj<span class="op">,</span> cloned)<span class="op">;</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>  <span class="cf">for</span> (<span class="kw">let</span> key <span class="kw">in</span> obj) {</span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> (obj<span class="op">.</span><span class="fu">hasOwnProperty</span>(key)) {</span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>      cloned[key] <span class="op">=</span> <span class="fu">deepClone</span>(obj[key]<span class="op">,</span> map)<span class="op">;</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>    }</span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a>  }</span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>  <span class="cf">return</span> cloned<span class="op">;</span></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<p>面试官对我考虑循环引用的处理比较满意。</p>
<p><strong>前端框架原理</strong> “说说Vue的响应式原理？”
我从Object.defineProperty讲到Proxy，解释了依赖收集和派发更新的过程，还提到了Vue3的优化。</p>
<p>“React的虚拟DOM有什么优势？”
我解释了虚拟DOM的diff算法、批量更新、跨平台等优势，面试官又问了Fiber架构的改进。</p>
<p><strong>项目经验分享</strong> 我详细介绍了之前负责的一个电商项目：
“这是一个移动端的电商应用，日活大概10万。我主要负责商品详情页和购物车模块。最大的挑战是性能优化，因为商品图片很多，页面加载很慢。”</p>
<p>“我采用了几个优化策略：1. 图片懒加载和WebP格式；2.
路由懒加载和代码分割；3. 使用CDN和浏览器缓存；4.
关键渲染路径优化。最终首屏加载时间从3.2秒优化到1.8秒。”</p>
<p>面试官对这个案例很感兴趣，问了很多技术细节。</p>
<p><strong>算法编程题</strong>
题目是”合并两个有序链表”，我用递归和迭代两种方法都实现了：</p>
<div class="sourceCode" id="cb2"><pre
class="sourceCode javascript"><code class="sourceCode javascript"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co">// 递归版本</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="kw">function</span> <span class="fu">mergeTwoLists</span>(l1<span class="op">,</span> l2) {</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a>  <span class="cf">if</span> (<span class="op">!</span>l1) <span class="cf">return</span> l2<span class="op">;</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a>  <span class="cf">if</span> (<span class="op">!</span>l2) <span class="cf">return</span> l1<span class="op">;</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>  <span class="cf">if</span> (l1<span class="op">.</span><span class="at">val</span> <span class="op">&lt;=</span> l2<span class="op">.</span><span class="at">val</span>) {</span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a>    l1<span class="op">.</span><span class="at">next</span> <span class="op">=</span> <span class="fu">mergeTwoLists</span>(l1<span class="op">.</span><span class="at">next</span><span class="op">,</span> l2)<span class="op">;</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> l1<span class="op">;</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>  } <span class="cf">else</span> {</span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>    l2<span class="op">.</span><span class="at">next</span> <span class="op">=</span> <span class="fu">mergeTwoLists</span>(l1<span class="op">,</span> l2<span class="op">.</span><span class="at">next</span>)<span class="op">;</span></span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> l2<span class="op">;</span></span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>  }</span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a>}</span></code></pre></div>
<p>面试官让我分析时间复杂度O(m+n)和空间复杂度O(m+n)，还问了迭代版本的空间复杂度优化。</p>
<h3 id="二面技术深度面试---75分钟">二面（技术深度面试 - 75分钟）</h3>
<p>面试官是微信事业群的技术专家，是个很有经验的架构师。这轮面试更注重技术深度和系统设计。</p>
<p><strong>系统设计题</strong>
“设计一个类似微信朋友圈的前端架构，需要支持千万级用户。”</p>
<p>我的设计思路： 1.
<strong>整体架构</strong>：采用微前端架构，按功能模块拆分 2.
<strong>技术选型</strong>：React + TypeScript + Webpack Module
Federation 3. <strong>状态管理</strong>：Redux + RTK Query处理复杂状态
4. <strong>性能优化</strong>： - 虚拟滚动处理长列表 -
图片懒加载和渐进式加载 - Service Worker缓存静态资源 -
CDN分发和边缘计算</p>
<p>面试官问：“如何处理朋友圈的实时更新？”
我回答：“可以用WebSocket建立长连接，结合心跳机制保持连接。对于新消息，采用增量更新而不是全量刷新。还可以用Intersection
Observer API优化可视区域的更新。”</p>
<p><strong>技术深度挖掘</strong> “说说Webpack的打包原理？”
我从入口文件开始，解释了依赖图构建、模块解析、代码转换、chunk分割、输出文件等整个流程。还提到了Tree
Shaking和Code Splitting的实现原理。</p>
<p>“如何优化首屏加载性能？” 我分享了一个实际案例：
“我们项目最初首屏加载要4秒，通过以下优化降到了1.5秒： 1.
路由懒加载，按页面分割代码 2. 关键CSS内联，非关键CSS异步加载 3.
图片使用WebP格式，并实现渐进式加载 4.
使用Preload和Prefetch优化资源加载时机 5. 开启Gzip压缩，减少传输体积”</p>
<p><strong>前端工程化</strong> 面试官问我对前端工程化的理解。我说：
“前端工程化主要解决开发效率和代码质量问题。包括： 1.
构建工具：Webpack、Vite等 2. 代码规范：ESLint、Prettier、Husky 3.
测试体系：单元测试、集成测试、E2E测试 4. CI/CD：自动化构建、测试、部署
5. 监控体系：错误监控、性能监控、用户行为分析”</p>
<p>“我们团队建立了完整的工程化体系，代码质量和开发效率都有明显提升。”</p>
<h3 id="三面综合能力面试---60分钟">三面（综合能力面试 - 60分钟）</h3>
<p>面试官是部门负责人，一个很有技术深度的管理者。这轮面试更像是技术交流。</p>
<p><strong>技术视野和学习能力</strong> “你如何看待前端技术的发展趋势？”
我说：“我觉得前端正在向几个方向发展： 1.
全栈化：Node.js、Serverless让前端工程师能处理更多后端逻辑 2.
工程化：构建工具、开发流程越来越成熟 3. 跨端统一：React
Native、Flutter等跨端方案 4. 智能化：AI辅助开发、自动化测试等 5.
性能优化：Web Assembly、边缘计算等新技术”</p>
<p>“我平时会关注这些技术的发展，也会在项目中尝试应用。比如我们最近在尝试用Vite替换Webpack，开发体验确实有提升。”</p>
<p><strong>团队协作经验</strong> 面试官问我在团队中的角色。我说：
“我在团队中主要负责技术方案设计和代码review。我们团队有个技术分享制度，每周都会有人分享新技术或者踩坑经验。我分享过Vue3的Composition
API、Webpack5的Module Federation等主题。”</p>
<p>“我还负责新人的技术指导，会帮他们制定学习计划，review他们的代码。我觉得帮助别人成长也是自己成长的过程。”</p>
<p><strong>技术难题解决</strong>
面试官问我遇到过最难的技术问题。我分享了一个内存泄漏的案例：
“我们的单页应用在使用一段时间后会变得很卡，通过Chrome
DevTools发现是内存泄漏。最后定位到是事件监听器没有正确移除，还有一些闭包引用了DOM元素。”</p>
<p>“解决方案是：1. 统一事件管理，组件销毁时自动清理；2.
使用WeakMap避免强引用；3.
建立内存监控机制。这个问题让我对JavaScript的内存管理有了更深的理解。”</p>
<p><strong>职业规划</strong> “你在腾讯希望达到什么目标？”
我说：“短期目标是快速融入团队，在微信这样的大型项目中积累经验。中期希望能在前端架构方面有所贡献，比如参与基础组件库或者工程化工具的建设。长期希望能成长为技术专家，在前端领域有一定的影响力。”</p>
<p>面试官点头表示认可，还和我聊了一些微信前端团队的技术挑战和发展方向。</p>
<h3 id="四面hr面试---30分钟">四面（HR面试 - 30分钟）</h3>
<p>HR是个很专业的小姐姐，主要了解一些基本情况和求职动机。</p>
<p>她问了我为什么选择腾讯，我说主要是看重腾讯的技术实力和平台影响力，特别是微信这样的国民级产品。她还问了薪资期望、入职时间等常规问题。</p>
<p>比较有意思的是她问我：“你觉得自己最大的优势是什么？”我说是学习能力和解决问题的能力，然后举了几个具体的例子。</p>
<p>整个HR面试很轻松，感觉更像是双向了解。</p>
<h2 id="面试官反馈">面试官反馈</h2>
<p><strong>一面反馈</strong>：面试官说我的基础知识比较扎实，项目经验也不错，但建议我多关注一些新技术的发展。</p>
<p><strong>二面反馈</strong>：技术专家认为我的系统设计思路比较清晰，对性能优化也有实际经验，很适合微信这样的大型项目。</p>
<p><strong>三面反馈</strong>：部门负责人说我的技术视野和学习能力都很好，团队协作意识也不错，很期待我加入团队。</p>
<h2 id="个人感受和总结">个人感受和总结</h2>
<p>整个面试过程让我收获很大，不仅是技术方面的交流，更重要的是感受到了腾讯的技术文化。每个面试官都很专业，问题也很有针对性。</p>
<p><strong>最大的收获</strong>： 1. 对前端技术有了更系统的认识 2.
学会了如何更好地表达技术方案 3. 意识到了自己在系统设计方面的不足</p>
<p><strong>面试过程中的感受</strong>：
腾讯的面试官都很nice，即使我有些问题回答得不够好，他们也会耐心引导。特别是二面的技术专家，在我设计系统架构时给了很多有价值的建议。</p>
<h2 id="面试结果">面试结果</h2>
<p>面试结束后一周，HR通知我通过了所有面试，并详细介绍了offer的具体内容。薪资比我预期的要高一些，福利也很不错。</p>
<h2 id="经验总结">经验总结</h2>
<p><strong>技术准备建议：</strong> 1.
<strong>基础要扎实</strong>：JavaScript、浏览器原理、网络协议等基础知识必须过关
2.
<strong>项目要深入</strong>：准备2-3个有代表性的项目，能讲清楚技术选型和优化过程
3.
<strong>算法要练习</strong>：虽然不是算法岗，但基本的数据结构和算法还是要会的
4. <strong>新技术要关注</strong>：了解前端发展趋势，有自己的思考</p>
<p><strong>面试技巧：</strong> 1.
<strong>表达要清晰</strong>：技术方案要能用简洁的语言表达清楚 2.
<strong>思路要开阔</strong>：遇到问题要能从多个角度思考 3.
<strong>态度要谦虚</strong>：不会的问题坦诚说明，展现学习意愿 4.
<strong>互动要积极</strong>：可以主动提问，展现对技术的热情</p>
<p><strong>给后来者的建议：</strong>
腾讯的面试确实有一定难度，但只要准备充分，展现出扎实的技术功底和良好的学习能力，还是很有机会的。最重要的是要保持对技术的热情，这是面试官最看重的品质。</p>
<p>希望我的经历能对大家有所帮助，祝愿每个前端工程师都能找到心仪的工作！</p>
</body>
</html>
