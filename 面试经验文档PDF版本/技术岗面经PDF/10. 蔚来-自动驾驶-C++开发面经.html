<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>10. 蔚来-自动驾驶-C++开发面经</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
  </style>
  <link rel="stylesheet" href="data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9IHByZSBjb2RlIHsgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IHBhZGRpbmc6IDA7IH0gYmxvY2txdW90ZSB7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDE2cHg7IGNvbG9yOiAjNmE3MzdkOyB9IHRhYmxlIHsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgd2lkdGg6IDEwMCU7IG1hcmdpbjogMTZweCAwOyB9IHRoLCB0ZCB7IGJvcmRlcjogMXB4IHNvbGlkICNkZmUyZTU7IHBhZGRpbmc6IDhweCAxMnB4OyB0ZXh0LWFsaWduOiBsZWZ0OyB9IHRoIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsgZm9udC13ZWlnaHQ6IDYwMDsgfSB1bCwgb2wgeyBwYWRkaW5nLWxlZnQ6IDI0cHg7IH0gbGkgeyBtYXJnaW46IDRweCAwOyB9" />
</head>
<body>
<header id="title-block-header">
<h1 class="title">10. 蔚来-自动驾驶-C++开发面经</h1>
</header>
<h1 id="蔚来-自动驾驶-c开发面经">蔚来-自动驾驶-C++开发面经</h1>
<h2 id="作者信息">作者信息</h2>
<p><strong>昵称</strong>：零重力啤酒
<strong>背景</strong>：硕士毕业，计算机科学专业，有4年C++开发经验。熟悉高性能计算、实时系统开发，对自动驾驶算法有一定了解。
<strong>面试结果</strong>：通过</p>
<h2 id="个人背景介绍">个人背景介绍</h2>
<p>我硕士学的计算机科学，研究方向是计算机视觉。毕业后在一家机器人公司做C++开发，主要负责机器人控制系统和感知算法的开发。</p>
<p>我们的机器人系统需要实时处理传感器数据，我主要负责： 1.
实时控制系统开发，使用C++11/14，要求毫秒级响应 2.
传感器数据处理，包括激光雷达、摄像头数据融合 3.
路径规划算法实现，使用A*、RRT等算法 4.
系统性能优化，包括内存管理、多线程优化</p>
<p>最有成就感的项目是优化了机器人的路径规划算法，通过并行计算和缓存优化，将规划时间从100ms降低到20ms，大大提升了机器人的响应速度。</p>
<p>选择蔚来主要是被自动驾驶技术的挑战性吸引。自动驾驶是机器人技术的最高形态，涉及感知、决策、控制的完整链路，我希望能在这个领域深入发展。</p>
<h2 id="面试准备过程">面试准备过程</h2>
<p>收到面试通知后，我花了3周时间准备： -
深入学习了自动驾驶的技术栈，包括感知、定位、规划、控制 -
研究了蔚来的自动驾驶技术方案和产品特点 -
复习了C++高级特性，如智能指针、模板、并发编程 -
准备了机器人项目的详细技术方案</p>
<h2 id="面试流程详细描述">面试流程详细描述</h2>
<h3 id="一面c技术基础面试---70分钟">一面（C++技术基础面试 -
70分钟）</h3>
<p>面试官是蔚来自动驾驶团队的资深C++工程师。</p>
<p><strong>C++语言深度考察</strong>
“说说C++11的智能指针，以及它们的使用场景？”
我详细回答了unique_ptr、shared_ptr、weak_ptr的特点和使用场景，特别强调了在自动驾驶系统中避免内存泄漏的重要性。</p>
<p>“C++的内存模型和多线程编程？”
我解释了C++11的内存模型、原子操作、内存序等概念，并分享了在实时系统中使用无锁编程的经验。</p>
<p><strong>实时系统开发经验</strong> 我分享了机器人控制系统的开发经验：
“我们的控制系统要求10ms的控制周期，通过实时线程调度、内存预分配、避免动态内存分配等手段，确保了系统的实时性。”</p>
<p><strong>算法编程题</strong>
题目是”实现一个线程安全的环形缓冲区”，我用C++实现了无锁版本。</p>
<h3 id="二面自动驾驶系统设计面试---80分钟">二面（自动驾驶系统设计面试 -
80分钟）</h3>
<p>面试官是蔚来自动驾驶的架构师。</p>
<p><strong>自动驾驶系统架构</strong>
“设计一个自动驾驶的感知系统，需要处理多传感器数据融合。”</p>
<p>我的设计包括： 1. 数据采集层：激光雷达、摄像头、毫米波雷达 2.
预处理层：数据同步、坐标变换、噪声滤除 3.
感知算法层：目标检测、跟踪、分类 4. 融合层：多传感器数据融合、置信度评估
5. 输出层：统一的目标列表和环境模型</p>
<p><strong>性能优化策略</strong> “如何优化感知算法的实时性？”
我回答：“1. 算法并行化，利用多核CPU；2. GPU加速，使用CUDA；3.
内存优化，减少数据拷贝；4. 算法优化，如ROI裁剪；5.
硬件加速，使用专用芯片。”</p>
<h3 id="三面技术视野面试---60分钟">三面（技术视野面试 - 60分钟）</h3>
<p>面试官是自动驾驶技术负责人。</p>
<p><strong>自动驾驶技术理解</strong>
“你如何看待自动驾驶技术的发展现状和挑战？”
我分析了技术成熟度、法规政策、商业化路径等多个维度的挑战。</p>
<p><strong>团队协作经验</strong>
我分享了与算法团队合作优化感知算法的经验。</p>
<h3 id="四面hr面试---30分钟">四面（HR面试 - 30分钟）</h3>
<p>HR了解个人情况和求职动机。</p>
<h2 id="面试官反馈">面试官反馈</h2>
<p>C++基础扎实，实时系统开发经验丰富，对自动驾驶技术理解深入。</p>
<h2 id="面试结果">面试结果</h2>
<p>面试结束后5天收到offer，薪资比预期高。</p>
<h2 id="经验总结">经验总结</h2>
<p><strong>技术准备建议</strong>： 1.
<strong>C++基础要深入</strong>：现代C++特性、内存管理、并发编程 2.
<strong>实时系统要理解</strong>：实时性要求、性能优化、系统设计 3.
<strong>自动驾驶要了解</strong>：感知、决策、控制的基本原理 4.
<strong>算法要扎实</strong>：数据结构、算法设计、复杂度分析</p>
<p><strong>给后来者的建议</strong>：
蔚来的C++开发岗位技术要求很高，特别是对实时性和性能的要求。如果你对自动驾驶技术感兴趣，蔚来是个很好的平台。</p>
<p>希望我的经历能对大家有所帮助！</p>
</body>
</html>
