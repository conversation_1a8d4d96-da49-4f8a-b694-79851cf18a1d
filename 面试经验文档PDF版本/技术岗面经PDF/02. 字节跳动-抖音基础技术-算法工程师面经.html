<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>02. 字节跳动-抖音基础技术-算法工程师面经</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
    /* CSS for syntax highlighting */
    html { -webkit-text-size-adjust: 100%; }
    pre > code.sourceCode { white-space: pre; position: relative; }
    pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
    pre > code.sourceCode > span:empty { height: 1.2em; }
    .sourceCode { overflow: visible; }
    code.sourceCode > span { color: inherit; text-decoration: inherit; }
    div.sourceCode { margin: 1em 0; }
    pre.sourceCode { margin: 0; }
    @media screen {
    div.sourceCode { overflow: auto; }
    }
    @media print {
    pre > code.sourceCode { white-space: pre-wrap; }
    pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
    }
    pre.numberSource code
      { counter-reset: source-line 0; }
    pre.numberSource code > span
      { position: relative; left: -4em; counter-increment: source-line; }
    pre.numberSource code > span > a:first-child::before
      { content: counter(source-line);
        position: relative; left: -1em; text-align: right; vertical-align: baseline;
        border: none; display: inline-block;
        -webkit-touch-callout: none; -webkit-user-select: none;
        -khtml-user-select: none; -moz-user-select: none;
        -ms-user-select: none; user-select: none;
        padding: 0 4px; width: 4em;
        color: #aaaaaa;
      }
    pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
    div.sourceCode
      {   }
    @media screen {
    pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
    }
    code span.al { color: #ff0000; font-weight: bold; } /* Alert */
    code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
    code span.at { color: #7d9029; } /* Attribute */
    code span.bn { color: #40a070; } /* BaseN */
    code span.bu { color: #008000; } /* BuiltIn */
    code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
    code span.ch { color: #4070a0; } /* Char */
    code span.cn { color: #880000; } /* Constant */
    code span.co { color: #60a0b0; font-style: italic; } /* Comment */
    code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
    code span.do { color: #ba2121; font-style: italic; } /* Documentation */
    code span.dt { color: #902000; } /* DataType */
    code span.dv { color: #40a070; } /* DecVal */
    code span.er { color: #ff0000; font-weight: bold; } /* Error */
    code span.ex { } /* Extension */
    code span.fl { color: #40a070; } /* Float */
    code span.fu { color: #06287e; } /* Function */
    code span.im { color: #008000; font-weight: bold; } /* Import */
    code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
    code span.kw { color: #007020; font-weight: bold; } /* Keyword */
    code span.op { color: #666666; } /* Operator */
    code span.ot { color: #007020; } /* Other */
    code span.pp { color: #bc7a00; } /* Preprocessor */
    code span.sc { color: #4070a0; } /* SpecialChar */
    code span.ss { color: #bb6688; } /* SpecialString */
    code span.st { color: #4070a0; } /* String */
    code span.va { color: #19177c; } /* Variable */
    code span.vs { color: #4070a0; } /* VerbatimString */
    code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <link rel="stylesheet" href="data:text/css;base64,Ym9keSB7IGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdQaW5nRmFuZyBTQycsICdIaXJhZ2lubyBTYW5zIEdCJywgJ01pY3Jvc29mdCBZYUhlaScsICdIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7IGxpbmUtaGVpZ2h0OiAxLjY7IGNvbG9yOiAjMzMzOyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfSBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHsgY29sb3I6ICMyYzNlNTA7IG1hcmdpbi10b3A6IDI0cHg7IG1hcmdpbi1ib3R0b206IDE2cHg7IH0gaDEgeyBmb250LXNpemU6IDI4cHg7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQ5OGRiOyBwYWRkaW5nLWJvdHRvbTogMTBweDsgfSBoMiB7IGZvbnQtc2l6ZTogMjRweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNiZGMzYzc7IHBhZGRpbmctYm90dG9tOiA4cHg7IH0gaDMgeyBmb250LXNpemU6IDIwcHg7IH0gY29kZSB7IGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7IHBhZGRpbmc6IDJweCA0cHg7IGJvcmRlci1yYWRpdXM6IDNweDsgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7IH0gcHJlIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxNnB4OyBvdmVyZmxvdy14OiBhdXRvOyB9IHByZSBjb2RlIHsgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IHBhZGRpbmc6IDA7IH0gYmxvY2txdW90ZSB7IGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsgbWFyZ2luOiAwOyBwYWRkaW5nLWxlZnQ6IDE2cHg7IGNvbG9yOiAjNmE3MzdkOyB9IHRhYmxlIHsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgd2lkdGg6IDEwMCU7IG1hcmdpbjogMTZweCAwOyB9IHRoLCB0ZCB7IGJvcmRlcjogMXB4IHNvbGlkICNkZmUyZTU7IHBhZGRpbmc6IDhweCAxMnB4OyB0ZXh0LWFsaWduOiBsZWZ0OyB9IHRoIHsgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsgZm9udC13ZWlnaHQ6IDYwMDsgfSB1bCwgb2wgeyBwYWRkaW5nLWxlZnQ6IDI0cHg7IH0gbGkgeyBtYXJnaW46IDRweCAwOyB9" />
</head>
<body>
<header id="title-block-header">
<h1 class="title">02. 字节跳动-抖音基础技术-算法工程师面经</h1>
</header>
<h1
id="字节跳动-抖音基础技术-算法工程师面经">字节跳动-抖音基础技术-算法工程师面经</h1>
<h2 id="作者信息">作者信息</h2>
<p><strong>昵称</strong>：把风吹进海
<strong>背景</strong>：硕士毕业，计算机科学专业，研究方向是推荐系统。有2年推荐算法工程师经验，熟悉深度学习和大规模机器学习系统。
<strong>面试结果</strong>：通过</p>
<h2 id="个人背景">个人背景</h2>
<p>我硕士期间主要研究推荐系统，发过几篇相关的论文。毕业后在一家视频平台做推荐算法，主要负责视频推荐的召回和排序算法优化。我们平台日活大概500万，我负责的推荐系统每天要处理几千万次推荐请求。</p>
<p>在技术栈方面，我比较熟悉Python、TensorFlow、PyTorch这些机器学习工具，也有一些大数据处理的经验，用过Spark、Hive等。对推荐系统的工程化实现比较了解，包括特征工程、模型训练、在线服务等全链路。</p>
<p>选择字节跳动主要是被抖音的技术挑战吸引。抖音的用户规模和数据量都是顶级的，推荐算法的复杂度也很高。我觉得在这样的平台上能学到最前沿的推荐技术，对个人成长很有帮助。</p>
<h2 id="面试准备过程">面试准备过程</h2>
<p>收到面试通知后，我做了比较充分的准备：</p>
<p><strong>算法基础复习</strong>：重新梳理了机器学习和深度学习的基础知识，特别是推荐系统相关的算法，包括协同过滤、矩阵分解、深度学习推荐模型等。</p>
<p><strong>字节技术研究</strong>：深入了解了字节跳动的技术栈和开源项目，特别关注了他们在推荐系统方面的技术分享。看了很多字节技术团队的博客文章和会议分享。</p>
<p><strong>项目案例整理</strong>：把之前做过的推荐系统项目重新梳理，准备了详细的技术方案、效果数据、遇到的问题和解决方案。</p>
<p><strong>编程练习</strong>：刷了一些算法题，主要是数据结构和算法基础，还有一些机器学习相关的编程题。</p>
<h2 id="面试流程详细描述">面试流程详细描述</h2>
<h3 id="一面算法基础面试---75分钟">一面（算法基础面试 - 75分钟）</h3>
<p>面试官是抖音推荐算法团队的资深工程师，主要考察算法基础和编程能力。面试在字节跳动北京总部进行，环境很好。</p>
<p><strong>机器学习基础</strong>
面试官先问了一些机器学习的基础问题：</p>
<p>“说说逻辑回归和线性回归的区别？”
我从损失函数、激活函数、应用场景等方面进行了对比，还提到了逻辑回归其实是广义线性模型的特例。</p>
<p>“解释一下梯度下降的原理，以及SGD、Adam等优化器的区别？”
我详细解释了梯度下降的数学原理，然后对比了不同优化器的特点：SGD简单但可能震荡，Momentum加入了动量项，Adam结合了动量和自适应学习率。</p>
<p><strong>推荐系统专业问题</strong> “推荐系统中的冷启动问题如何解决？”
我回答：“冷启动分为用户冷启动、物品冷启动和系统冷启动。用户冷启动可以通过人口统计学特征、热门推荐等方式；物品冷启动可以利用内容特征、专家标注等；系统冷启动需要积累初始数据。”</p>
<p>“如何评估推荐系统的效果？”
我从离线评估和在线评估两个维度回答：离线指标包括准确率、召回率、AUC、NDCG等；在线指标包括点击率、转化率、用户停留时间、用户活跃度等。</p>
<p><strong>编程题</strong> 题目是”Top K
频繁元素”，我用堆排序的方法解决：</p>
<div class="sourceCode" id="cb1"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="im">import</span> heapq</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> collections <span class="im">import</span> Counter</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="kw">def</span> topKFrequent(nums, k):</span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a>    count <span class="op">=</span> Counter(nums)</span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> heapq.nlargest(k, count.keys(), key<span class="op">=</span>count.get)</span></code></pre></div>
<p>面试官让我分析时间复杂度O(n + k log
n)，还问了如果数据量很大无法放入内存怎么处理，我提到了外部排序和分布式处理的思路。</p>
<p><strong>项目经验深入讨论</strong>
我详细介绍了之前负责的视频推荐项目：
“我们的推荐系统采用召回+排序的两阶段架构。召回阶段用了协同过滤、内容相似度、热门推荐等多路召回，能从千万级视频库中召回几百个候选。排序阶段用深度学习模型，特征包括用户画像、视频特征、上下文特征等。”</p>
<p>面试官问了很多技术细节，比如特征工程怎么做、模型如何训练、如何处理数据倾斜等。我感觉他对推荐系统很有经验，问题都很专业。</p>
<h3 id="二面系统设计面试---90分钟">二面（系统设计面试 - 90分钟）</h3>
<p>面试官是抖音推荐系统的技术专家，主要考察大规模推荐系统的设计能力。这轮面试难度明显提升。</p>
<p><strong>推荐系统架构设计</strong>
面试官给了一个开放性问题：“设计一个类似抖音的短视频推荐系统，需要支持10亿用户，每天推荐请求100亿次。”</p>
<p>我的设计思路：</p>
<p><strong>整体架构</strong>： 1. 离线部分：数据处理、特征工程、模型训练
2. 近线部分：实时特征计算、模型更新 3.
在线部分：召回、排序、重排、反馈收集</p>
<p><strong>召回层设计</strong>： -
多路召回：协同过滤、内容相似度、热门推荐、用户兴趣标签等 -
每路召回几十到几百个候选 - 使用向量检索技术（如Faiss）加速相似度计算</p>
<p><strong>排序层设计</strong>： -
深度学习模型，如Wide&amp;Deep、DeepFM、DIN等 -
特征包括：用户画像、视频特征、交互特征、上下文特征 -
模型服务用TensorFlow Serving或自研框架</p>
<p><strong>工程架构</strong>： - 微服务架构，各模块独立部署 -
使用Redis缓存热点数据 - Kafka处理实时数据流 -
分布式存储（HDFS、HBase）</p>
<p>面试官对我的设计比较认可，然后深入问了一些细节：</p>
<p>“如何处理推荐系统的实时性要求？”
我回答：“通过流式计算框架（如Flink）实时更新用户兴趣，使用在线学习技术快速适应用户行为变化，还可以通过实时特征服务提供最新的上下文信息。”</p>
<p>“如何解决推荐系统的多样性问题？”
我提到了几种方法：重排序阶段引入多样性约束、使用DPP（Determinantal Point
Process）、在损失函数中加入多样性正则项等。</p>
<p><strong>深度学习模型优化</strong> 面试官问了一些模型优化的问题：</p>
<p>“如何处理推荐模型中的样本不平衡问题？”
我回答：“可以通过负采样、调整样本权重、使用Focal
Loss等方法。在实际项目中，我们用了分层采样，对不同类型的负样本采用不同的采样率。”</p>
<p>“模型如何做A/B测试？”
我详细介绍了推荐系统A/B测试的设计：用户分桶、流量分配、指标监控、统计显著性检验等。</p>
<h3 id="三面业务理解面试---60分钟">三面（业务理解面试 - 60分钟）</h3>
<p>面试官是抖音推荐算法团队的负责人，主要考察对业务的理解和技术视野。</p>
<p><strong>抖音业务深度理解</strong>
面试官问：“你觉得抖音的推荐算法和其他平台有什么不同？”</p>
<p>我回答：“抖音是短视频平台，有几个特点：1.
内容消费速度快，用户反馈密集；2. 视频内容丰富，需要多模态理解；3.
用户行为模式独特，刷视频有很强的沉浸感；4.
创作者生态复杂，需要平衡用户体验和创作者激励。”</p>
<p>“这些特点对推荐算法提出了什么挑战？”
我继续分析：“首先是实时性要求很高，需要快速捕捉用户兴趣变化；其次是多模态特征融合，需要理解视频、音频、文本等多种信息；还有就是冷启动问题，新用户和新视频都需要快速找到合适的匹配。”</p>
<p><strong>技术发展趋势讨论</strong>
“你觉得推荐系统未来的发展方向是什么？” 我谈了几个观点： 1.
多模态融合会越来越重要，特别是视觉和语言的结合 2.
大模型在推荐系统中的应用，如用GPT做内容理解 3.
强化学习在推荐中的应用，优化长期用户价值 4.
隐私保护技术，如联邦学习、差分隐私等</p>
<p><strong>团队协作经验</strong>
面试官问我在团队中的角色和贡献。我分享了一个跨团队合作的项目：
“我们要优化视频推荐的多样性，需要和产品、运营、数据团队密切配合。我主要负责算法设计和效果评估，通过A/B测试验证方案效果。最终用户停留时长提升了8%，多样性指标也有明显改善。”</p>
<p><strong>职业规划</strong> “你在字节跳动希望达到什么目标？”
我说：“短期希望能快速融入团队，在抖音这样的大规模推荐系统中积累经验。中期希望能在推荐算法的某个细分领域做出贡献，比如多模态推荐或者实时推荐。长期希望能成长为推荐系统的技术专家。”</p>
<p>面试官对我的回答比较满意，还和我聊了一些字节跳动的技术文化和发展机会。</p>
<h3 id="四面hr面试---30分钟">四面（HR面试 - 30分钟）</h3>
<p>HR是个很专业的小姐姐，主要了解个人情况和求职动机。</p>
<p>她问了我为什么选择字节跳动，我说主要是被抖音的技术挑战吸引，希望在这样的平台上做出有影响力的工作。她还问了薪资期望、入职时间等常规问题。</p>
<p>比较有意思的是她问我：“字节跳动的工作节奏比较快，你如何看待？”我说我比较喜欢快节奏的工作环境，能让人快速成长，而且我觉得做有价值的事情，加班也是值得的。</p>
<h2 id="面试官反馈">面试官反馈</h2>
<p><strong>一面反馈</strong>：面试官说我的算法基础比较扎实，项目经验也不错，但建议我多关注一些前沿的推荐算法技术。</p>
<p><strong>二面反馈</strong>：技术专家认为我的系统设计思路比较清晰，对大规模推荐系统的理解也比较深入，很适合抖音这样的业务场景。</p>
<p><strong>三面反馈</strong>：团队负责人说我对业务的理解很到位，技术视野也比较开阔，团队协作能力也不错。</p>
<h2 id="个人感受和总结">个人感受和总结</h2>
<p>整个面试过程让我收获很大，不仅是技术方面的交流，更重要的是感受到了字节跳动对技术的重视和对人才的渴求。</p>
<p><strong>最大的收获</strong>： 1. 对大规模推荐系统有了更深的认识 2.
了解了抖音推荐算法的一些技术细节 3.
意识到了自己在多模态学习方面的不足</p>
<p><strong>面试过程中的感受</strong>：
字节的面试官都很专业，问题很有深度。特别是二面的系统设计，让我对推荐系统的工程化实现有了新的思考。面试官也很nice，会耐心听我的想法，并给出建设性的建议。</p>
<h2 id="面试结果">面试结果</h2>
<p>面试结束后一周，HR通知我通过了所有面试。offer的薪资比我预期的要高，福利也很不错。最重要的是能加入抖音这样的顶级推荐系统团队。</p>
<h2 id="经验总结">经验总结</h2>
<p><strong>技术准备建议</strong>： 1.
<strong>算法基础要扎实</strong>：机器学习、深度学习、推荐系统的理论基础必须过关
2.
<strong>项目经验要深入</strong>：准备1-2个有代表性的推荐系统项目，能讲清楚技术细节和业务价值
3.
<strong>系统设计要练习</strong>：多练习大规模推荐系统的设计，包括架构、存储、计算等各个方面
4.
<strong>前沿技术要关注</strong>：了解推荐系统的最新发展，如多模态推荐、大模型应用等</p>
<p><strong>面试技巧</strong>： 1.
<strong>表达要清晰</strong>：技术方案要能用简洁的语言表达清楚，逻辑要清晰
2. <strong>思考要深入</strong>：不要只停留在表面，要能深入分析问题的本质
3.
<strong>业务要理解</strong>：要对抖音的业务特点有深入理解，能结合业务场景思考技术方案
4. <strong>态度要积极</strong>：展现出对技术的热情和持续学习的能力</p>
<p><strong>给后来者的建议</strong>：
字节跳动的算法岗位竞争很激烈，但如果你有扎实的技术基础和丰富的项目经验，还是很有机会的。最重要的是要对推荐系统有真正的理解和热情，这是面试官最看重的。</p>
<p>希望我的经历能对大家有所帮助，祝愿每个算法工程师都能找到心仪的工作！</p>
</body>
</html>
