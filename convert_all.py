#!/usr/bin/env python3
import os
import markdown
import subprocess
import tempfile
from pathlib import Path
import time

def convert_single_file(md_file_path, pdf_output_path):
    """Convert a single MD file to PDF using Chrome"""
    try:
        # Read MD file
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # Convert to HTML
        html_content = markdown.markdown(md_content, extensions=['tables', 'fenced_code'])
        
        # Create styled HTML
        styled_html = f"""<!DOCTYPE html>
<html><head><meta charset="utf-8"><title>{os.path.basename(md_file_path)}</title>
<style>
@page {{ size: A4; margin: 0.75in; }}
body {{ font-family: system-ui, sans-serif; line-height: 1.6; color: #333; }}
h1, h2, h3 {{ color: #2c3e50; margin-top: 1.5em; margin-bottom: 0.5em; }}
h1 {{ border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
h2 {{ border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }}
code {{ background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
pre {{ background: #f8f9fa; padding: 12px; border-radius: 5px; }}
table {{ border-collapse: collapse; width: 100%; margin: 1em 0; }}
th, td {{ border: 1px solid #ddd; padding: 6px; }}
th {{ background: #f2f2f2; font-weight: bold; }}
blockquote {{ border-left: 4px solid #3498db; margin: 1em 0; padding-left: 1em; color: #666; }}
ul, ol {{ margin: 1em 0; padding-left: 1.5em; }}
li {{ margin: 0.3em 0; }}
</style></head><body>{html_content}</body></html>"""
        
        # Create temp HTML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(styled_html)
            temp_path = f.name
        
        try:
            # Use Chrome to convert
            chrome_path = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
            if os.path.exists(chrome_path):
                cmd = [
                    chrome_path, 
                    '--headless', 
                    '--disable-gpu', 
                    '--no-sandbox',
                    '--print-to-pdf=' + pdf_output_path, 
                    'file://' + temp_path
                ]
                result = subprocess.run(cmd, capture_output=True, timeout=15)
                
                if os.path.exists(pdf_output_path):
                    return True
            
            return False
            
        finally:
            try:
                os.unlink(temp_path)
            except:
                pass
                
    except Exception as e:
        return False

def convert_folder_files(source_folder, target_folder, folder_name):
    """Convert all MD files in a folder"""
    source_path = Path(source_folder)
    target_path = Path(target_folder)
    target_path.mkdir(parents=True, exist_ok=True)
    
    md_files = sorted(list(source_path.glob("*.md")))
    success_count = 0
    
    print(f"\n📁 Converting {folder_name} ({len(md_files)} files)...")
    
    for i, md_file in enumerate(md_files, 1):
        pdf_file = target_path / (md_file.stem + ".pdf")
        
        if convert_single_file(md_file, str(pdf_file)):
            print(f"✓ [{i:2d}/{len(md_files)}] {md_file.name}")
            success_count += 1
        else:
            print(f"✗ [{i:2d}/{len(md_files)}] {md_file.name}")
        
        # Small delay to prevent overwhelming Chrome
        time.sleep(0.2)
    
    print(f"   {success_count}/{len(md_files)} files converted successfully")
    return success_count, len(md_files)

if __name__ == "__main__":
    base_path = "/Users/<USER>/Desktop/龙井面经"
    
    print("🔄 Starting full Markdown to PDF conversion...")
    print("=" * 60)
    
    # Convert all files
    tech_success, tech_total = convert_folder_files(
        f"{base_path}/技术岗面经",
        f"{base_path}/PDF面经/技术岗面经",
        "技术岗面经"
    )
    
    non_tech_success, non_tech_total = convert_folder_files(
        f"{base_path}/非技术岗面经",
        f"{base_path}/PDF面经/非技术岗面经",
        "非技术岗面经"
    )
    
    # Final summary
    total_success = tech_success + non_tech_success
    total_files = tech_total + non_tech_total
    
    print("\n" + "=" * 60)
    print("📊 FINAL SUMMARY:")
    print(f"   技术岗面经: {tech_success}/{tech_total} files")
    print(f"   非技术岗面经: {non_tech_success}/{non_tech_total} files")
    print(f"   Total: {total_success}/{total_files} files converted ({total_success/total_files*100:.1f}%)")
    
    if total_success == total_files:
        print("🎉 All files converted successfully!")
    else:
        print(f"⚠️  {total_files - total_success} files failed to convert")
    
    print(f"\n📂 PDF files saved to: {base_path}/PDF面经/")