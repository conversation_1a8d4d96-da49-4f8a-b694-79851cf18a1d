#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量改进面试经验文档脚本
快速处理剩余文件的基础改进
"""

import os
import re
import random
from pathlib import Path

# 随机昵称池（更丰富的昵称）
RANDOM_NICKNAMES = [
    "代码搬砖工", "技术小白", "职场新人", "算法菜鸟", "前端小能手", 
    "后端攻城狮", "产品小白兔", "运营小达人", "设计师阿强", "数据分析师小李",
    "测试小姐姐", "架构师老王", "全栈工程师", "移动端开发者", "云计算爱好者",
    "AI算法工程师", "区块链探索者", "大数据挖掘师", "网络安全专家", "DevOps工程师",
    "技术探索者", "编程爱好者", "开发小能手", "系统架构师", "数据库管理员",
    "前端切图仔", "后端CRUD工程师", "算法调参侠", "产品汪", "运营喵",
    "UI设计狮", "测试小蜜蜂", "运维小哥", "安全攻防手", "机器学习研究员",
    "Python攻城狮", "Java开发者", "Go语言爱好者", "Rust布道师", "C++码农",
    "JavaScript忍者", "数据科学家", "云原生架构师", "微服务专家", "容器化工程师",
    "区块链开发者", "游戏引擎工程师", "音视频技术专家", "图形学研究员", "编译器工程师",
    "嵌入式开发者", "物联网工程师", "5G网络工程师", "量化交易员", "金融科技专家"
]

def get_random_nickname():
    """获取随机昵称，避免重复"""
    return random.choice(RANDOM_NICKNAMES)

def remove_time_references(content):
    """移除时间相关信息"""
    # 移除具体年份月份
    content = re.sub(r'20\d{2}年\d{1,2}月', '', content)
    content = re.sub(r'20\d{2}年', '', content)
    content = re.sub(r'\d{1,2}月份?', '', content)
    
    # 移除面试时间行
    content = re.sub(r'\*\*面试时间\*\*：[^\n]*\n', '', content)
    
    # 移除其他时间表述
    content = re.sub(r'[去今明]年', '', content)
    content = re.sub(r'最近几个月', '最近', content)
    content = re.sub(r'上个月', '之前', content)
    
    return content

def improve_basic_content(content, file_path):
    """基础内容改进"""
    # 1. 更换昵称
    nickname_pattern = r'\*\*昵称\*\*：([^\n]*)'
    content = re.sub(nickname_pattern, f'**昵称**：{get_random_nickname()}', content)
    
    # 2. 移除时间信息
    content = remove_time_references(content)
    
    # 3. 改进模板化表述
    content = content.replace('技术基础扎实，项目经验丰富，对技术发展趋势有敏锐洞察', 
                            '有丰富的项目经验和扎实的技术基础，对行业发展有深入理解')
    
    content = content.replace('选择XX是因为其在技术领域的领先地位和良好的工程师文化', 
                            '选择这家公司主要是看重其技术实力和发展前景')
    
    # 4. 改进面试结果部分
    content = content.replace('面试过程专业，成功拿到offer。公司技术氛围很好，期待未来的工作。',
                            '面试过程很专业，最终成功拿到offer。对未来的工作很期待。')
    
    return content

def process_file(file_path):
    """处理单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 基础改进
        improved_content = improve_basic_content(content, file_path)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(improved_content)
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始批量基础改进面试经验文档...")
    
    # 获取所有面经文件
    tech_dir = Path("技术岗面经")
    non_tech_dir = Path("非技术岗面经")
    
    all_files = []
    if tech_dir.exists():
        all_files.extend(list(tech_dir.glob("*.md")))
    if non_tech_dir.exists():
        all_files.extend(list(non_tech_dir.glob("*.md")))
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    
    success_count = 0
    for file_path in all_files:
        if process_file(file_path):
            success_count += 1
            print(f"✅ 已处理: {file_path.name}")
    
    print(f"\n批量基础改进完成！成功处理 {success_count}/{len(all_files)} 个文件")

if __name__ == "__main__":
    main()
